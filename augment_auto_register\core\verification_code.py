#!/usr/bin/env python3
"""
Augment 自动注册工具 - 验证码获取模块
通过IMAP连接QQ邮箱获取Augment验证码，支持邮件解析和验证码提取
"""

import time
import imaplib
import email
import re
from typing import Optional, Dict, Any, List
from email.message import EmailMessage

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.config_manager import get_config
from utils.logger import get_logger
from utils.helpers import safe_sleep, extract_digits_from_text


class VerificationCodeHandler:
    """验证码获取处理器"""
    
    def __init__(self, config_manager=None):
        """
        初始化验证码处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager or get_config()
        self.logger = get_logger("VerificationCode")
        
        # 获取邮箱配置
        self.email_config = self.config.get_email_config()
        self.imap_config = self.email_config.get('imap', {})
        
        # IMAP连接参数
        self.imap_server = self.imap_config.get('server', 'imap.qq.com')
        self.imap_port = self.imap_config.get('port', 993)
        self.use_ssl = self.imap_config.get('use_ssl', True)
        self.username = self.imap_config.get('username', '')
        self.password = self.imap_config.get('password', '')
        
        # 验证配置
        if not self.username or not self.password:
            raise ValueError("IMAP邮箱配置不完整，请检查username和password")
        
        self.imap_connection: Optional[imaplib.IMAP4_SSL] = None
        
        self.logger.info(f"验证码处理器初始化完成 (服务器: {self.imap_server})")
    
    def connect_to_imap(self) -> bool:
        """
        连接到IMAP服务器
        
        Returns:
            是否连接成功
        """
        try:
            self.logger.info(f"连接到IMAP服务器: {self.imap_server}:{self.imap_port}")
            
            if self.use_ssl:
                self.imap_connection = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                self.imap_connection = imaplib.IMAP4(self.imap_server, self.imap_port)
            
            # 登录
            self.imap_connection.login(self.username, self.password)
            
            # 选择收件箱
            self.imap_connection.select('inbox')
            
            self.logger.success("IMAP连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"IMAP连接失败: {e}")
            self.imap_connection = None
            return False
    
    def close_connection(self) -> None:
        """关闭IMAP连接"""
        try:
            if self.imap_connection:
                self.imap_connection.close()
                self.imap_connection.logout()
                self.imap_connection = None
                self.logger.info("IMAP连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭IMAP连接失败: {e}")
    
    def get_verification_code(self, target_email: str, max_wait: int = 90) -> Optional[str]:
        """
        从邮箱获取验证码
        
        Args:
            target_email: 目标邮箱地址
            max_wait: 最大等待时间（秒）
            
        Returns:
            验证码，如果获取失败返回None
        """
        self.logger.info(f"开始获取验证码，目标邮箱: {target_email}")
        
        # 连接到IMAP服务器
        if not self.connect_to_imap():
            return None
        
        try:
            # 获取当前邮件数量作为基准
            initial_count = self._get_email_count()
            self.logger.info(f"当前邮箱中有 {initial_count} 封邮件，等待新邮件...")
            
            start_time = time.time()
            check_interval = 3  # 每3秒检查一次
            
            while time.time() - start_time < max_wait:
                try:
                    # 检查是否有新邮件
                    current_count = self._get_email_count()
                    
                    if current_count > initial_count:
                        self.logger.info(f"发现新邮件！当前 {current_count} 封，之前 {initial_count} 封")
                        
                        # 检查新邮件
                        verification_code = self._check_new_emails(target_email, initial_count)
                        
                        if verification_code:
                            self.logger.success(f"✅ 成功获取验证码: {verification_code}")
                            return verification_code
                        
                        # 更新基准邮件数量
                        initial_count = current_count
                    else:
                        elapsed = int(time.time() - start_time)
                        self.logger.debug(f"等待新邮件... ({elapsed}/{max_wait}s)")
                    
                    safe_sleep(check_interval)
                    
                except Exception as e:
                    self.logger.error(f"检查邮件时出错: {e}")
                    safe_sleep(check_interval)
            
            self.logger.error(f"❌ 获取验证码超时（{max_wait}秒）")
            return None
            
        finally:
            self.close_connection()
    
    def _get_email_count(self) -> int:
        """获取邮箱中的邮件数量"""
        try:
            status, messages = self.imap_connection.search(None, 'ALL')
            if status == 'OK' and messages[0]:
                return len(messages[0].split())
            return 0
        except Exception as e:
            self.logger.error(f"获取邮件数量失败: {e}")
            return 0
    
    def _check_new_emails(self, target_email: str, start_from: int) -> Optional[str]:
        """
        检查新邮件中是否有验证码
        
        Args:
            target_email: 目标邮箱地址
            start_from: 从第几封邮件开始检查
            
        Returns:
            验证码，如果没有找到返回None
        """
        try:
            # 获取所有邮件ID
            status, messages = self.imap_connection.search(None, 'ALL')
            
            if status != 'OK' or not messages[0]:
                return None
            
            email_ids = messages[0].split()
            
            # 只检查新邮件
            new_email_ids = email_ids[start_from:]
            
            for email_id in new_email_ids:
                try:
                    # 获取邮件内容
                    status, msg_data = self.imap_connection.fetch(email_id, '(RFC822)')
                    
                    if status == 'OK':
                        email_body = msg_data[0][1]
                        email_message = email.message_from_bytes(email_body)
                        
                        # 检查是否为Augment验证邮件
                        if self._is_augment_verification_email(email_message, target_email):
                            self.logger.info("✅ 找到Augment验证码邮件！")
                            
                            # 提取验证码
                            content = self._get_email_content(email_message)
                            verification_code = self._extract_verification_code(content)
                            
                            if verification_code:
                                return verification_code
                            else:
                                self.logger.warning("⚠️ 邮件中未找到6位验证码")
                        
                except Exception as e:
                    self.logger.error(f"处理邮件 {email_id} 时出错: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查新邮件失败: {e}")
            return None

    def _is_augment_verification_email(self, email_message, target_email: str) -> bool:
        """
        判断是否为Augment验证邮件

        Args:
            email_message: 邮件消息对象
            target_email: 目标邮箱地址

        Returns:
            是否为Augment验证邮件
        """
        try:
            # 获取邮件头信息
            sender = email_message.get('From', '').lower()
            subject = email_message.get('Subject', '').lower()
            to_address = email_message.get('To', '').lower()

            self.logger.debug(f"检查邮件 - 发件人: {sender}")
            self.logger.debug(f"检查邮件 - 主题: {subject}")
            self.logger.debug(f"检查邮件 - 收件人: {to_address}")

            # 首先检查邮件是否发送给目标邮箱
            if target_email.lower() not in to_address:
                self.logger.debug(f"跳过邮件：不是发送给目标邮箱 {target_email}")
                return False

            self.logger.info(f"✅ 邮件发送给目标邮箱: {target_email}")

            # 检查是否为Augment相关邮件
            augment_keywords = [
                'augment',
                'augment code',
                'augment sign up',
                'augmentcode'
            ]

            # 检查发件人
            is_augment_sender = any(keyword in sender for keyword in augment_keywords)

            # 检查主题
            is_augment_subject = any(keyword in subject for keyword in augment_keywords)

            # 排除其他服务的邮件
            excluded_keywords = ['cursor', 'cursor.sh']
            is_excluded = any(keyword in sender or keyword in subject for keyword in excluded_keywords)

            if is_excluded:
                self.logger.debug("跳过邮件：被排除的服务邮件")
                return False

            if is_augment_sender or is_augment_subject:
                self.logger.info("✅ 确认为Augment验证邮件")
                return True

            # 如果发件人和主题都不匹配，但邮件内容可能包含验证码，也检查一下
            content = self._get_email_content(email_message)
            if 'verification code is:' in content.lower():
                self.logger.info("✅ 邮件内容包含验证码格式，认定为验证邮件")
                return True

            self.logger.debug("跳过邮件：不是Augment验证邮件")
            return False

        except Exception as e:
            self.logger.error(f"判断邮件类型失败: {e}")
            return False

    def _get_email_content(self, email_message) -> str:
        """
        获取邮件内容

        Args:
            email_message: 邮件消息对象

        Returns:
            邮件内容文本
        """
        content = ""

        try:
            if email_message.is_multipart():
                # 处理多部分邮件
                for part in email_message.walk():
                    content_type = part.get_content_type()

                    if content_type == "text/plain":
                        payload = part.get_payload(decode=True)
                        if payload:
                            content += payload.decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        payload = part.get_payload(decode=True)
                        if payload:
                            content += payload.decode('utf-8', errors='ignore')
            else:
                # 处理单部分邮件
                payload = email_message.get_payload(decode=True)
                if payload:
                    content = payload.decode('utf-8', errors='ignore')

            return content

        except Exception as e:
            self.logger.error(f"获取邮件内容失败: {e}")
            return ""

    def _extract_verification_code(self, content: str) -> Optional[str]:
        """
        从邮件内容中提取验证码

        Args:
            content: 邮件内容

        Returns:
            验证码，如果没有找到返回None
        """
        self.logger.debug(f"开始提取验证码，邮件内容长度: {len(content)}")

        # Augment验证码的特定模式
        augment_patterns = [
            r'Your verification code is:\s*(\d{6})',       # Your verification code is: 036681
            r'verification code is:\s*(\d{6})',            # verification code is: 036681
            r'code is:\s*(\d{6})',                         # code is: 036681
        ]

        # 尝试Augment特定模式
        for i, pattern in enumerate(augment_patterns):
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                self.logger.debug(f"Augment模式 {i+1} '{pattern}' 找到: {matches}")
                for match in matches:
                    code = match if isinstance(match, str) else match[0]
                    if code and len(code) == 6 and code.isdigit():
                        self.logger.success(f"✅ 找到Augment验证码: {code}")
                        return code

        # 如果没找到，尝试通用模式
        general_patterns = [
            r'verification code[:\s]*([0-9]{6})',          # verification code: 123456
            r'verify[:\s]*([0-9]{6})',                     # verify: 123456
            r'code[:\s]*([0-9]{6})',                       # code: 123456
            r'Your code[:\s]*([0-9]{6})',                  # Your code: 123456
        ]

        for i, pattern in enumerate(general_patterns):
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                self.logger.debug(f"通用模式 {i+1} '{pattern}' 找到: {matches}")
                for match in matches:
                    code = match if isinstance(match, str) else match[0]
                    if code and len(code) == 6 and code.isdigit():
                        self.logger.info(f"✅ 找到验证码: {code}")
                        return code

        # 最后尝试提取所有6位数字
        six_digit_code = extract_digits_from_text(content, 6)
        if six_digit_code:
            # 过滤掉明显不是验证码的数字
            excluded_codes = ['2024', '2025', '0000', '1111', '2222', '123456', '000000']
            if six_digit_code not in excluded_codes:
                self.logger.info(f"✅ 找到可能的验证码: {six_digit_code}")
                return six_digit_code

        # 显示邮件内容用于调试
        self.logger.warning("❌ 未找到验证码")
        self.logger.debug("邮件内容（用于调试）:")

        # 显示包含数字的行
        lines = content.split('\n')
        for i, line in enumerate(lines[:20]):  # 只显示前20行
            if re.search(r'\d', line):  # 包含数字的行
                self.logger.debug(f"第{i+1}行: {line.strip()}")

        return None

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_connection()

    def __str__(self) -> str:
        """返回验证码处理器的字符串表示"""
        return f"VerificationCodeHandler(server={self.imap_server}, user={self.username})"

    def __repr__(self) -> str:
        return self.__str__()
