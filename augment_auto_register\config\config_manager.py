#!/usr/bin/env python3
"""
Augment 自动注册工具 - 配置管理模块
负责加载和验证YAML配置文件，提供配置项获取接口
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional
import yaml


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 config/config.yaml
        """
        if config_path is None:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent
            config_path = project_root / "config" / "config.yaml"
        
        self.config_path = Path(config_path)
        self.config_data: Dict[str, Any] = {}
        self._load_config()
        self._validate_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def _validate_config(self) -> None:
        """验证配置文件的必要字段"""
        required_sections = ['email', 'browser', 'registration']
        
        for section in required_sections:
            if section not in self.config_data:
                raise ValueError(f"配置文件缺少必要部分: [{section}]")
        
        # 验证邮箱配置
        email_config = self.config_data['email']
        if 'suffix' not in email_config:
            raise ValueError("邮箱配置缺少 'suffix' 字段")
        
        if 'imap' not in email_config:
            raise ValueError("邮箱配置缺少 'imap' 部分")
        
        imap_config = email_config['imap']
        required_imap_fields = ['server', 'port', 'username', 'password']
        for field in required_imap_fields:
            if field not in imap_config:
                raise ValueError(f"IMAP配置缺少 '{field}' 字段")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'email.suffix'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_email_config(self) -> Dict[str, Any]:
        """获取邮箱配置"""
        return self.config_data.get('email', {})
    
    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置"""
        return self.config_data.get('browser', {})
    
    def get_registration_config(self) -> Dict[str, Any]:
        """获取注册流程配置"""
        return self.config_data.get('registration', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config_data.get('logging', {})
    
    def reload(self) -> None:
        """重新加载配置文件"""
        self._load_config()
        self._validate_config()
    
    def __str__(self) -> str:
        """返回配置信息的字符串表示"""
        return f"ConfigManager(config_path={self.config_path})"
    
    def __repr__(self) -> str:
        return self.__str__()


# 全局配置实例
_config_instance: Optional[ConfigManager] = None


def get_config(config_path: Optional[str] = None) -> ConfigManager:
    """
    获取全局配置实例（单例模式）
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        ConfigManager实例
    """
    global _config_instance
    
    if _config_instance is None:
        _config_instance = ConfigManager(config_path)
    
    return _config_instance


def reload_config() -> None:
    """重新加载全局配置"""
    global _config_instance
    
    if _config_instance is not None:
        _config_instance.reload()


if __name__ == "__main__":
    # 测试配置管理器
    try:
        config = ConfigManager()
        print("✅ 配置文件加载成功")
        print(f"邮箱后缀: {config.get('email.suffix')}")
        print(f"IMAP服务器: {config.get('email.imap.server')}")
        print(f"浏览器无头模式: {config.get('browser.headless')}")
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        sys.exit(1)
