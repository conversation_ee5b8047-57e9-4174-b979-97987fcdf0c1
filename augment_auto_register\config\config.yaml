# Augment 自动注册工具配置文件

# 邮箱配置
email:
  # 邮箱后缀（随机8位数字 + 后缀）
  suffix: "@otudt.xyz"
  
  # IMAP邮箱服务配置（用于接收验证码）
  imap:
    server: "imap.qq.com"
    port: 993
    username: "<EMAIL>"
    password: "mvgwslbmwlwwdada"  # QQ邮箱应用密码
    use_ssl: true

# 浏览器配置
browser:
  # 是否无头模式
  headless: false
  # 页面加载超时时间（秒）
  timeout: 30
  # Chrome浏览器路径（留空自动检测）
  chrome_path: ""

# 注册流程配置
registration:
  # 等待验证码的最大时间（秒）
  wait_timeout: 90
  # 人机验证最大等待时间（秒）
  verification_wait: 15
  # 最大重试次数
  max_retries: 3
  # 页面加载等待时间（秒）
  page_load_wait: 2

# 日志配置
logging:
  # 日志级别：DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  # 是否保存日志到文件
  save_to_file: true
  # 日志文件路径
  log_file: "logs/augment_register.log"
