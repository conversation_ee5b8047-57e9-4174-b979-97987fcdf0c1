#!/usr/bin/env python3
"""
Augment 自动注册工具 - 注册流程核心模块
整合所有模块，实现完整的三步注册流程：邮箱输入+人机验证、验证码输入、完成注册
"""

import time
from typing import Optional, Dict, Any, List

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.browser_manager import BrowserManager
from core.email_manager import EmailManager
from core.human_verification import HumanVerificationHandler
from core.verification_code import VerificationCodeHandler
from config.config_manager import get_config
from utils.logger import get_logger
from utils.helpers import safe_sleep, get_random_wait_time, save_json_result


class AugmentAutoRegister:
    """Augment自动注册器"""
    
    def __init__(self, config_manager=None):
        """
        初始化自动注册器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager or get_config()
        self.logger = get_logger("AugmentRegister")
        
        # 初始化各个管理器
        self.browser = BrowserManager(self.config)
        self.email_manager = EmailManager(self.config)
        self.human_verifier = None  # 在浏览器启动后初始化
        self.verification_code_handler = VerificationCodeHandler(self.config)
        
        # 注册信息
        self.generated_email: Optional[str] = None
        self.verification_code: Optional[str] = None
        self.registration_result: Optional[Dict[str, Any]] = None
        
        self.logger.info("Augment自动注册器初始化完成")
    
    def run_registration(self) -> Optional[Dict[str, Any]]:
        """
        运行完整的注册流程
        
        Returns:
            注册结果字典，失败返回None
        """
        self.logger.info("🚀 开始Augment自动注册流程")
        self.logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # 1. 生成注册邮箱
            self.generated_email = self.email_manager.generate_email()
            self.logger.info(f"📧 生成注册邮箱: {self.generated_email}")
            
            # 2. 启动浏览器
            if not self.browser.setup_browser():
                self.logger.error("❌ 浏览器启动失败")
                return None
            
            # 初始化人机验证处理器
            self.human_verifier = HumanVerificationHandler(self.browser, self.config)
            
            # 3. 执行三步注册流程
            if not self._step1_email_and_verification():
                return None
            
            if not self._step2_verification_code():
                return None
            
            if not self._step3_complete_registration():
                return None
            
            # 4. 生成注册结果
            end_time = time.time()
            duration = end_time - start_time
            
            self.registration_result = {
                'success': True,
                'email': self.generated_email,
                'verification_code': self.verification_code,
                'duration': duration,
                'timestamp': end_time,
                'final_url': self.browser.get_current_url(),
                'message': '注册成功完成'
            }
            
            # 保存结果到文件
            result_file = save_json_result(self.registration_result)
            self.registration_result['result_file'] = result_file
            
            self.logger.success("🎉 注册流程成功完成！")
            self.logger.info(f"📧 注册邮箱: {self.generated_email}")
            self.logger.info(f"⏱️ 耗时: {duration:.1f}秒")
            self.logger.info(f"💾 结果已保存到: {result_file}")
            
            return self.registration_result
            
        except Exception as e:
            self.logger.error(f"❌ 注册流程异常: {e}")
            return None
            
        finally:
            # 清理资源
            self._cleanup()
    
    def _step1_email_and_verification(self) -> bool:
        """
        步骤1：邮箱输入和人机验证
        
        Returns:
            是否成功完成
        """
        self.logger.info("📝 步骤1: 邮箱输入和人机验证")
        
        try:
            # 1.1 导航到注册页面
            if not self.browser.navigate_to_page('https://app.augmentcode.com'):
                self.logger.error("❌ 导航到注册页面失败")
                return False
            
            # 1.2 等待页面重定向到Auth0登录页面
            max_redirect_wait = 15
            for i in range(max_redirect_wait):
                current_url = self.browser.get_current_url()
                self.logger.debug(f"当前URL: {current_url}")
                
                if 'login.augmentcode.com' in current_url and 'identifier' in current_url:
                    self.logger.success("✅ 已到达Auth0登录页面")
                    break
                
                if i < max_redirect_wait - 1:
                    safe_sleep(1)
            else:
                self.logger.warning("⚠️ 未检测到重定向，继续执行...")
            
            # 1.3 等待页面完全加载
            self.logger.info("⏳ 等待页面完全加载...")
            safe_sleep(2)  # 按照你的要求等待2秒
            
            # 1.4 填写邮箱
            if not self._fill_email_input():
                return False

            # 1.5 等待5秒让人机验证有时间自动触发
            self.logger.info("⏳ 等待5秒让人机验证自动触发...")
            safe_sleep(5)

            # 1.6 处理人机验证
            if not self.human_verifier.handle_verification():
                self.logger.error("❌ 人机验证失败")
                return False

            # 1.7 点击Continue按钮
            if not self._click_continue_button():
                return False
            
            self.logger.success("✅ 步骤1完成：邮箱输入和人机验证")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤1失败: {e}")
            return False
    
    def _fill_email_input(self) -> bool:
        """填写邮箱输入框"""
        self.logger.info(f"📧 填写邮箱: {self.generated_email}")
        
        # 基于你提供的HTML元素，使用多个选择器尝试
        email_selectors = [
            "@name=username",           # 主要选择器
            "@id=username",             # 备用选择器
            "@inputmode=email",         # 根据inputmode属性
            "@autocomplete=email",      # 根据autocomplete属性
            "input[name='username']",   # CSS选择器
            "input[id='username']",
            "input[inputmode='email']",
            "input[autocomplete='email']",
            "input[autofocus]",         # 有autofocus属性
        ]
        
        for selector in email_selectors:
            if self.browser.fill_input(selector, self.generated_email):
                self.logger.success(f"✅ 邮箱填写成功 (选择器: {selector})")
                return True
        
        self.logger.error("❌ 未找到邮箱输入框")
        return False
    
    def _click_continue_button(self) -> bool:
        """点击Continue按钮"""
        self.logger.info("🔘 点击Continue按钮")
        
        # 基于你提供的HTML元素
        continue_selectors = [
            "@type=submit",                         # 主要选择器
            "@name=action",                         # 根据name属性
            "@data-action-button-primary=true",     # 根据data属性
            "button[type='submit']",                # CSS选择器
            "button[name='action']",
            "button[data-action-button-primary='true']",
            "button[value='default']",
            "text:Continue",                        # 文本选择器
        ]
        
        for selector in continue_selectors:
            if self.browser.click_element(selector):
                self.logger.success(f"✅ Continue按钮点击成功 (选择器: {selector})")
                safe_sleep(get_random_wait_time('submit'))
                return True
        
        self.logger.error("❌ 未找到Continue按钮")
        return False

    def _step2_verification_code(self) -> bool:
        """
        步骤2：获取并输入验证码

        Returns:
            是否成功完成
        """
        self.logger.info("🔢 步骤2: 获取并输入验证码")

        try:
            # 2.1 等待验证码页面加载
            self.logger.info("⏳ 等待验证码页面加载...")
            safe_sleep(3)

            # 2.2 等待验证码输入框出现
            if not self._wait_for_verification_input():
                return False

            # 2.3 获取验证码
            wait_timeout = self.config.get('registration.wait_timeout', 90)
            self.verification_code = self.verification_code_handler.get_verification_code(
                self.generated_email, wait_timeout
            )

            if not self.verification_code:
                self.logger.error("❌ 获取验证码失败")
                return False

            # 2.4 输入验证码
            if not self._fill_verification_code():
                return False

            # 2.5 点击Continue按钮（验证码页面的）
            if not self._click_continue_button():
                return False

            self.logger.success("✅ 步骤2完成：验证码输入")
            return True

        except Exception as e:
            self.logger.error(f"❌ 步骤2失败: {e}")
            return False

    def _wait_for_verification_input(self) -> bool:
        """等待验证码输入框出现"""
        self.logger.info("🔍 查找验证码输入框...")

        # 验证码输入框的可能选择器
        verification_selectors = [
            "@name=code",                    # Auth0常用
            "@id=code",
            "@placeholder*=code",            # 包含code的placeholder
            "@placeholder*=Code",
            "input[name='code']",
            "input[id='code']",
            "input[placeholder*='code']",
            "input[maxlength='6']",          # 6位验证码
            "input[pattern='[0-9]*']",       # 数字输入框
        ]

        max_wait = 15
        for i in range(max_wait):
            for selector in verification_selectors:
                element = self.browser.find_element(selector, timeout=1)
                if element:
                    self.logger.success(f"✅ 找到验证码输入框 (选择器: {selector})")
                    return True

            if i < max_wait - 1:
                self.logger.debug(f"验证码输入框未找到，等待... ({i+1}/{max_wait})")
                safe_sleep(1)

        self.logger.error("❌ 验证码输入框查找超时")
        return False

    def _fill_verification_code(self) -> bool:
        """填写验证码"""
        self.logger.info(f"🔢 填写验证码: {self.verification_code}")

        # 验证码输入框选择器（与等待时使用相同的）
        verification_selectors = [
            "@name=code",
            "@id=code",
            "@placeholder*=code",
            "@placeholder*=Code",
            "input[name='code']",
            "input[id='code']",
            "input[placeholder*='code']",
            "input[maxlength='6']",
            "input[pattern='[0-9]*']",
        ]

        for selector in verification_selectors:
            if self.browser.fill_input(selector, self.verification_code):
                self.logger.success(f"✅ 验证码填写成功 (选择器: {selector})")
                safe_sleep(get_random_wait_time('input'))
                return True

        self.logger.error("❌ 验证码填写失败")
        return False

    def _step3_complete_registration(self) -> bool:
        """
        步骤3：完成注册

        Returns:
            是否成功完成
        """
        self.logger.info("🎯 步骤3: 完成注册")

        try:
            # 3.1 等待最终页面加载
            self.logger.info("⏳ 等待最终页面加载...")
            safe_sleep(5)

            # 3.2 先处理条款同意
            if self._handle_terms_agreement():
                self.logger.info("✅ 条款同意处理完成")
                # 模拟人类操作：同意条款后稍等一下
                self.logger.info("⏳ 等待2秒后点击注册按钮...")
                safe_sleep(2)

            # 3.3 然后点击最终注册按钮
            if self._click_final_register_button():
                self.logger.success("✅ 最终注册按钮点击成功")

            # 3.4 等待注册完成
            self.logger.info("⏳ 等待注册完成...")
            safe_sleep(5)

            # 3.5 检查注册是否成功
            current_url = self.browser.get_current_url()
            if 'app.augmentcode.com' in current_url and 'login' not in current_url:
                self.logger.success("✅ 步骤3完成：注册成功")
                return True
            else:
                self.logger.warning(f"⚠️ 注册状态不确定，当前URL: {current_url}")
                return True  # 暂时认为成功，后续可以添加更精确的检查

        except Exception as e:
            self.logger.error(f"❌ 步骤3失败: {e}")
            return False

    def _handle_terms_agreement(self) -> bool:
        """处理条款同意"""
        self.logger.info("🔍 查找条款同意复选框...")

        # 查找条款同意复选框的多种选择器
        terms_selectors = [
            "input[type='checkbox']",                           # 通用复选框
            "@type=checkbox",                                   # DrissionPage语法
            "xpath://input[@type='checkbox']",                  # XPath
            "xpath://input[@type='checkbox' and not(@disabled)]", # 未禁用的复选框
            "css:input[type='checkbox']:not([disabled])",      # CSS选择器
        ]

        for i, selector in enumerate(terms_selectors):
            self.logger.debug(f"尝试选择器 {i+1}: {selector}")
            element = self.browser.find_element(selector, timeout=2)
            if element:
                # 检查是否已经勾选
                is_checked = element.attr('checked')
                if is_checked:
                    self.logger.info("✅ 条款复选框已经勾选")
                    return True

                # 尝试点击
                self.logger.info(f"🔘 找到条款同意复选框，点击同意 (选择器: {selector})")
                if self.browser.click_element(selector):
                    # 点击后等待一下，模拟人类操作
                    safe_sleep(get_random_wait_time('click'))

                    # 验证是否成功勾选
                    is_checked_after = element.attr('checked')
                    if is_checked_after:
                        self.logger.success("✅ 条款同意复选框点击成功")
                        return True
                    else:
                        self.logger.warning("⚠️ 复选框点击后未勾选，继续尝试其他方法")
                        continue

        # 如果没有找到复选框，可能不需要同意条款
        self.logger.info("ℹ️ 未找到条款同意复选框，可能不需要此步骤")
        return True

    def _click_final_register_button(self) -> bool:
        """点击最终注册按钮"""
        self.logger.info("🔍 查找最终注册按钮...")

        # 最终注册按钮的可能选择器
        final_button_selectors = [
            "text:Sign up and start coding",                    # Augment特定文本
            "text:Sign up",                                     # 通用注册文本
            "text:Register",                                    # 注册按钮
            "text:Complete",                                    # 完成按钮
            "text:Finish",                                      # 结束按钮
            "text:Create Account",                              # 创建账户
            "text:Get Started",                                 # 开始使用
            "@type=submit",                                     # 提交按钮
            "button[type='submit']",                            # CSS选择器
            "xpath://button[@type='submit']",                   # XPath
            "xpath://button[contains(text(), 'Sign up')]",      # 包含Sign up文本
            "xpath://button[contains(text(), 'Register')]",     # 包含Register文本
            "xpath://input[@type='submit']",                    # input提交按钮
        ]

        for i, selector in enumerate(final_button_selectors):
            self.logger.debug(f"尝试选择器 {i+1}: {selector}")
            element = self.browser.find_element(selector, timeout=2)
            if element:
                # 检查按钮是否可用
                disabled = element.attr('disabled')
                if disabled and disabled != 'false':
                    self.logger.warning(f"按钮被禁用，跳过: {selector}")
                    continue

                # 尝试点击
                self.logger.info(f"🔘 找到注册按钮，准备点击 (选择器: {selector})")
                if self.browser.click_element(selector):
                    # 点击后等待，模拟人类操作
                    safe_sleep(get_random_wait_time('submit'))
                    self.logger.success(f"✅ 最终注册按钮点击成功")
                    return True
                else:
                    self.logger.warning(f"按钮点击失败，继续尝试其他选择器")
                    continue

        self.logger.warning("⚠️ 未找到可点击的最终注册按钮")

        # 检查是否已经自动跳转（可能不需要点击按钮）
        current_url = self.browser.get_current_url()
        if current_url and ('dashboard' in current_url or 'app' in current_url):
            self.logger.info("✅ 页面已自动跳转，注册可能已完成")
            return True

        return False

    def _cleanup(self) -> None:
        """清理资源"""
        try:
            if hasattr(self, 'verification_code_handler'):
                self.verification_code_handler.close_connection()

            if hasattr(self, 'browser') and self.browser.is_initialized:
                # 不自动关闭浏览器，让用户手动关闭以便查看结果
                self.logger.info("💡 浏览器保持打开状态，请手动关闭")

        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")

    def get_registration_result(self) -> Optional[Dict[str, Any]]:
        """获取注册结果"""
        return self.registration_result

    def __str__(self) -> str:
        """返回注册器的字符串表示"""
        return f"AugmentAutoRegister(email={self.generated_email})"

    def __repr__(self) -> str:
        return self.__str__()
