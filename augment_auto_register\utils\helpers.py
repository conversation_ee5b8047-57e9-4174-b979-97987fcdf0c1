#!/usr/bin/env python3
"""
Augment 自动注册工具 - 工具函数模块
提供通用的工具函数
"""

import time
import random
import string
import re
from typing import Dict, Tuple, Optional, Any
from pathlib import Path
import json


def get_random_wait_time(timing_type: str = 'default') -> float:
    """
    获取随机等待时间
    
    Args:
        timing_type: 等待类型
        
    Returns:
        随机等待时间（秒）
    """
    timing_config = {
        'page_load': (1.0, 3.0),           # 页面加载等待
        'input': (0.3, 0.8),               # 输入等待
        'click': (0.2, 0.5),               # 点击等待
        'submit': (0.5, 1.5),              # 提交等待
        'verification_check': (1.0, 2.0),  # 验证检查等待
        'email_check': (3.0, 5.0),         # 邮件检查等待
        'human_verification': (2.0, 4.0),  # 人机验证等待
        'default': (0.5, 1.0)              # 默认等待
    }
    
    min_time, max_time = timing_config.get(timing_type, timing_config['default'])
    return random.uniform(min_time, max_time)


def generate_random_digits(length: int = 8) -> str:
    """
    生成随机数字字符串
    
    Args:
        length: 数字长度
        
    Returns:
        随机数字字符串
    """
    return ''.join(random.choices(string.digits, k=length))


def generate_random_string(length: int = 8, 
                          include_digits: bool = True,
                          include_lowercase: bool = True,
                          include_uppercase: bool = False) -> str:
    """
    生成随机字符串
    
    Args:
        length: 字符串长度
        include_digits: 是否包含数字
        include_lowercase: 是否包含小写字母
        include_uppercase: 是否包含大写字母
        
    Returns:
        随机字符串
    """
    chars = ""
    if include_digits:
        chars += string.digits
    if include_lowercase:
        chars += string.ascii_lowercase
    if include_uppercase:
        chars += string.ascii_uppercase
    
    if not chars:
        chars = string.digits  # 默认使用数字
    
    return ''.join(random.choices(chars, k=length))


def validate_email_format(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否为有效邮箱格式
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def extract_digits_from_text(text: str, length: Optional[int] = None) -> Optional[str]:
    """
    从文本中提取数字
    
    Args:
        text: 输入文本
        length: 期望的数字长度，None表示提取所有数字
        
    Returns:
        提取的数字字符串，如果没有找到则返回None
    """
    digits = re.findall(r'\d+', text)
    
    if not digits:
        return None
    
    if length is None:
        return ''.join(digits)
    
    # 查找指定长度的数字
    for digit_group in digits:
        if len(digit_group) == length:
            return digit_group
    
    # 如果没有找到指定长度的数字，返回第一个数字组
    return digits[0] if digits else None


def safe_sleep(seconds: float) -> None:
    """
    安全的睡眠函数，处理中断
    
    Args:
        seconds: 睡眠时间（秒）
    """
    try:
        time.sleep(seconds)
    except KeyboardInterrupt:
        raise
    except Exception:
        pass  # 忽略其他异常


def retry_on_exception(max_retries: int = 3, 
                      delay: float = 1.0,
                      exceptions: Tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试间隔时间
        exceptions: 需要重试的异常类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        safe_sleep(delay)
                        continue
                    else:
                        raise last_exception
            
            return None
        return wrapper
    return decorator


def save_json_result(data: Dict[str, Any], 
                    filename: Optional[str] = None,
                    results_dir: str = "results") -> str:
    """
    保存JSON结果到文件
    
    Args:
        data: 要保存的数据
        filename: 文件名，None表示自动生成
        results_dir: 结果目录
        
    Returns:
        保存的文件路径
    """
    # 创建结果目录
    results_path = Path(results_dir)
    results_path.mkdir(exist_ok=True)
    
    # 生成文件名
    if filename is None:
        timestamp = int(time.time())
        filename = f"augment_register_result_{timestamp}.json"
    
    file_path = results_path / filename
    
    # 保存数据
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    return str(file_path)


def format_duration(seconds: float) -> str:
    """
    格式化持续时间
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 输入字符串
        max_length: 最大长度
        suffix: 截断后缀
        
    Returns:
        截断后的字符串
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def get_project_root() -> Path:
    """
    获取项目根目录
    
    Returns:
        项目根目录路径
    """
    return Path(__file__).parent.parent


if __name__ == "__main__":
    # 测试工具函数
    print("🧪 测试工具函数")
    
    # 测试随机等待时间
    wait_time = get_random_wait_time('page_load')
    print(f"随机等待时间: {wait_time:.2f}秒")
    
    # 测试随机数字生成
    random_digits = generate_random_digits(8)
    print(f"随机8位数字: {random_digits}")
    
    # 测试邮箱验证
    test_email = f"{random_digits}@example.com"
    is_valid = validate_email_format(test_email)
    print(f"邮箱 {test_email} 格式有效: {is_valid}")
    
    # 测试数字提取
    test_text = "Your verification code is: 123456"
    code = extract_digits_from_text(test_text, 6)
    print(f"从文本提取6位数字: {code}")
    
    # 测试时间格式化
    duration = format_duration(125.5)
    print(f"格式化时间: {duration}")
    
    print("✅ 工具函数测试完成")
