#!/usr/bin/env python3
"""
自毁式Token注入器
1. 注入token到插件
2. 自动打开VSCode
3. 监控登录状态
4. 登录成功后自动还原代码
"""

import os
import re
import shutil
import subprocess
import sqlite3
import json
import time
import stat
from pathlib import Path

class AutoInjectAndRestore:
    def __init__(self, token, tenant_url):
        self.token = token
        self.tenant_url = tenant_url
        self.plugin_path = None
        self.backup_path = None
        self.vscode_path = None

    def enable_file_write_permission(self, file_path):
        """启用文件写入权限"""
        try:
            print(f"🔓 正在启用文件写入权限: {file_path}")

            # 方法1: 使用Python的os.chmod
            try:
                # 获取当前权限
                current_permissions = os.stat(file_path).st_mode
                # 添加写入权限
                os.chmod(file_path, current_permissions | stat.S_IWRITE)
                print("✅ 使用Python成功启用写入权限")
                return True
            except Exception as e:
                print(f"⚠️  Python方法失败: {e}")

            # 方法2: 使用Windows attrib命令
            try:
                result = subprocess.run(
                    ['attrib', '-R', file_path],
                    capture_output=True,
                    text=True,
                    check=True
                )
                print("✅ 使用attrib命令成功移除只读属性")
                return True
            except subprocess.CalledProcessError as e:
                print(f"⚠️  attrib命令失败: {e}")

            # 方法3: 使用icacls命令
            try:
                username = os.getenv('USERNAME', 'Everyone')
                result = subprocess.run(
                    ['icacls', file_path, '/grant', f'{username}:F'],
                    capture_output=True,
                    text=True,
                    check=True
                )
                print("✅ 使用icacls命令成功授予完全控制权限")
                return True
            except subprocess.CalledProcessError as e:
                print(f"⚠️  icacls命令失败: {e}")

            print("❌ 所有权限启用方法都失败了")
            return False

        except Exception as e:
            print(f"❌ 启用文件写入权限时发生错误: {e}")
            return False
        
    def find_plugin(self):
        """查找Augment插件"""
        print("🔍 查找Augment插件...")
        
        home_dir = Path.home()
        extensions_dir = home_dir / '.vscode' / 'extensions'
        
        if not extensions_dir.exists():
            print("❌ VSCode扩展目录未找到")
            return False
            
        # 查找Augment插件
        augment_dirs = list(extensions_dir.glob('augment.vscode-augment-*'))
        if not augment_dirs:
            print("❌ 未找到Augment插件")
            return False
            
        # 使用最新版本
        latest_dir = sorted(augment_dirs)[-1]
        self.plugin_path = latest_dir / 'out' / 'extension.js'
        
        if not self.plugin_path.exists():
            print(f"❌ 插件文件不存在: {self.plugin_path}")
            return False
            
        print(f"✅ 找到插件: {self.plugin_path}")
        return True
        
    def find_vscode(self):
        """查找VSCode安装路径 - 优先选择真正的VSCode"""
        print("🔍 查找VSCode安装路径...")

        # 方法1: 先检查注册表（更准确）
        try:
            import winreg
            registry_keys = [
                # 用户安装版本
                (winreg.HKEY_CURRENT_USER,
                 r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{771FD6B0-FA20-440A-A002-3B3BAC16DC50}_is1"),
                # 系统安装版本
                (winreg.HKEY_LOCAL_MACHINE,
                 r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{EA457B21-F73E-494C-ACAB-524FDE069978}_is1"),
            ]

            for hkey, subkey in registry_keys:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        install_location = winreg.QueryValueEx(key, "InstallLocation")[0]
                        vscode_exe = os.path.join(install_location, "Code.exe")

                        if os.path.exists(vscode_exe):
                            self.vscode_path = vscode_exe
                            print(f"✅ 注册表找到VSCode: {self.vscode_path}")
                            return True
                except FileNotFoundError:
                    continue
        except ImportError:
            print("⚠️  winreg不可用，跳过注册表检测")

        # 方法2: 检查常见路径（避免Cursor）
        common_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe",
            r"C:\Program Files\Microsoft VS Code\Code.exe",
            r"C:\Program Files (x86)\Microsoft VS Code\Code.exe",
        ]

        for path in common_paths:
            if os.path.exists(path):
                self.vscode_path = path
                print(f"✅ 常见路径找到VSCode: {self.vscode_path}")
                return True

        # 方法3: 检查PATH（但过滤掉Cursor）
        import shutil as sh
        path_candidates = []

        for cmd in ["code", "code.exe"]:
            path = sh.which(cmd)
            if path and os.path.exists(path):
                path_candidates.append(os.path.abspath(path))

        # 过滤掉Cursor，优先选择VSCode
        for path in path_candidates:
            if "cursor" not in path.lower() and "Microsoft VS Code" in path:
                self.vscode_path = path
                print(f"✅ PATH找到VSCode: {self.vscode_path}")
                return True

        # 如果没有找到VSCode，但有其他候选
        if path_candidates:
            # 选择第一个不是Cursor的
            for path in path_candidates:
                if "cursor" not in path.lower():
                    self.vscode_path = path
                    print(f"⚠️  PATH找到可能的VSCode: {self.vscode_path}")
                    return True

            # 实在没办法，显示所有候选让用户选择
            print("🤔 找到多个候选，请确认:")
            for i, path in enumerate(path_candidates):
                print(f"  {i+1}. {path}")

            # 暂时使用第一个
            self.vscode_path = path_candidates[0]
            print(f"⚠️  使用第一个候选: {self.vscode_path}")
            return True

        print("❌ 未找到VSCode安装路径")
        return False
        
    def detect_variables(self, content):
        """检测关键变量"""
        print("� 检测关键变量...")
        variables = {}

        # 1. 检测Commands变量
        commands_patterns = [
            r'(\w+)\.commands\.executeCommand\("setContext","vscode-augment\.isLoggedIn"',
            r'(\w+)\.commands\.executeCommand\("setContext","vscode-augment\.useOAuth"'
        ]

        for pattern in commands_patterns:
            match = re.search(pattern, content)
            if match:
                variables['commands'] = match.group(1)
                print(f"✅ 找到commands变量: {variables['commands']}")
                break
        else:
            variables['commands'] = None

        # 2. 检测Session变量
        session_pattern = r'var (\$\w+)="augment\.sessions"'
        session_match = re.search(session_pattern, content)

        if session_match:
            variables['session'] = session_match.group(1)
            print(f"✅ 找到session变量: {variables['session']}")
        else:
            variables['session'] = None

        # 3. 检测Scopes变量
        scopes_pattern = r'(\w+)=\["email"\]'
        scopes_match = re.search(scopes_pattern, content)

        if scopes_match:
            variables['scopes'] = scopes_match.group(1)
            print(f"✅ 找到scopes变量: {variables['scopes']}")
        else:
            variables['scopes'] = None

        return variables

    def find_init_state_method(self, content):
        """查找initState方法"""
        print("🔍 查找initState方法...")

        patterns = [
            r'async initState\(\)\{[^}]*this\._readyEmitter\.fire\(\)[^}]*\}',
            r'async initState\(\)\{[\s\S]*?this\._readyEmitter\.fire\(\)[\s\S]*?\}'
        ]

        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                method = match.group(0)
                print(f"✅ 找到initState方法: {len(method)} 字符")
                return method

        print("❌ 未找到initState方法")
        return None

    def generate_injected_method(self, original_method, variables):
        """生成注入后的方法"""
        # 提取原始核心逻辑
        core_logic_pattern = r'this\._isLoggedIn=.*?this\._readyEmitter\.fire\(\)'
        core_match = re.search(core_logic_pattern, original_method)

        if not core_match:
            raise ValueError("无法提取原始方法的核心逻辑")

        core_logic = core_match.group(0)

        # 生成注入代码
        injected_method = f'''async initState(){{
        // � 自动注入OAuth token
        const hardcodedToken = "{self.token}";
        const hardcodedTenantURL = "{self.tenant_url}";

        if (hardcodedToken && hardcodedTenantURL) {{
            console.log('🔑 Auto-injecting OAuth token...');
            const sessionData = {{
                accessToken: hardcodedToken,
                tenantURL: hardcodedTenantURL,
                scopes: {variables['scopes'] or '["email"]'}
            }};

            try {{
                await this._context.secrets.store({variables['session'] or '"$k"'}, JSON.stringify(sessionData));
                console.log('✅ OAuth token injected successfully!');
            }} catch (error) {{
                console.error('❌ Token injection failed:', error);
            }}
        }}

        // 保持原始逻辑
        {core_logic}
    }}'''

        return injected_method

    def inject_token(self):
        """注入token到插件"""
        print("🚀 开始注入token...")

        # 备份原文件
        self.backup_path = self.plugin_path.with_suffix('.js.auto_backup')

        # 先启用备份目录的写入权限
        backup_dir = self.backup_path.parent
        try:
            if backup_dir.exists():
                current_permissions = backup_dir.stat().st_mode
                os.chmod(backup_dir, current_permissions | stat.S_IWRITE)
        except Exception as e:
            print(f"⚠️  无法修改备份目录权限: {e}")

        if not self.backup_path.exists():
            try:
                shutil.copy2(self.plugin_path, self.backup_path)
                print(f"💾 备份完成: {self.backup_path}")
            except PermissionError as e:
                print(f"⚠️  备份创建失败: {e}")
                print("🔄 尝试不同的备份方法...")
                try:
                    # 尝试简单的文件复制
                    with open(self.plugin_path, 'r', encoding='utf-8') as src:
                        with open(self.backup_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    print(f"💾 备份完成 (使用替代方法): {self.backup_path}")
                except Exception as backup_error:
                    print(f"❌ 备份创建完全失败: {backup_error}")
                    print("⚠️  将在没有备份的情况下继续，请手动备份文件")
        else:
            print("💾 备份文件已存在")

        # 读取文件
        content = self.plugin_path.read_text(encoding='utf-8')
        print(f"📖 读取文件: {len(content)} 字符")

        # 检测变量
        variables = self.detect_variables(content)

        # 查找initState方法
        original_method = self.find_init_state_method(content)
        if not original_method:
            return False

        # 生成注入后的方法
        try:
            injected_method = self.generate_injected_method(original_method, variables)
        except Exception as e:
            print(f"❌ 生成注入代码失败: {e}")
            return False

        # 替换方法
        new_content = content.replace(original_method, injected_method)

        if new_content == content:
            print("❌ 代码替换失败")
            return False

        # 启用文件写入权限
        if not self.enable_file_write_permission(str(self.plugin_path)):
            print("⚠️  无法启用文件写入权限，尝试继续...")

        # 写入文件
        try:
            with open(self.plugin_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print("✅ Token注入完成！")
            return True
        except PermissionError as e:
            print(f"❌ 权限错误: {e}")
            print("💡 请尝试以管理员身份运行此脚本")
            return False
        except Exception as e:
            print(f"❌ 写入失败: {e}")
            return False
            
    def open_vscode(self):
        """打开VSCode"""
        if not self.vscode_path:
            print("❌ VSCode路径未找到")
            return False
            
        try:
            print(f"🚀 正在打开VSCode: {self.vscode_path}")
            subprocess.Popen([self.vscode_path])
            return True
        except Exception as e:
            print(f"❌ 打开VSCode失败: {e}")
            return False
            
    def is_logged_in(self):
        """检查是否已登录"""
        try:
            # VSCode state数据库路径
            db_path = Path.home() / 'AppData' / 'Roaming' / 'Code' / 'User' / 'globalStorage' / 'state.vscdb'
            
            if not db_path.exists():
                return False
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询session键
            session_key = 'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}'
            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (session_key,))
            result = cursor.fetchone()
            
            conn.close()
            
            return result is not None
            
        except Exception as e:
            print(f"⚠️ 检查登录状态失败: {e}")
            return False
            
    def restore_backup(self, delete_backup=True):
        """还原备份文件并可选择删除备份"""
        if self.backup_path and self.backup_path.exists():
            try:
                # 1. 还原文件
                shutil.copy2(self.backup_path, self.plugin_path)
                print("✅ 代码已还原到原始状态")

                # 2. 删除备份文件（如果需要）
                if delete_backup:
                    try:
                        # 先启用备份文件的写入权限
                        if self.enable_file_write_permission(str(self.backup_path)):
                            print("🔓 备份文件权限已启用")

                        self.backup_path.unlink()  # 删除备份文件
                        print("🗑️  备份文件已删除")
                    except PermissionError as e:
                        print(f"⚠️  删除备份文件权限不足: {e}")
                        print("🔄 尝试使用系统命令删除...")
                        try:
                            # 尝试使用del命令强制删除
                            subprocess.run(['del', '/f', '/q', str(self.backup_path)],
                                         shell=True, check=True)
                            print("🗑️  备份文件已强制删除")
                        except subprocess.CalledProcessError:
                            print(f"💡 请手动删除备份文件: {self.backup_path}")
                    except Exception as e:
                        print(f"⚠️  删除备份文件失败: {e}")
                        print(f"💡 请手动删除: {self.backup_path}")

                return True
            except Exception as e:
                print(f"❌ 还原失败: {e}")
                return False
        else:
            print("❌ 备份文件不存在")
            return False
            
    def wait_for_login_and_restore(self, timeout=60):
        """等待登录成功后还原代码"""
        print("⏳ 等待登录成功...")
        print("💡 请在VSCode中等待自动登录完成")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_logged_in():
                print("\n🎉 检测到登录成功！")
                time.sleep(2)  # 等待2秒确保登录完全完成
                
                if self.restore_backup(delete_backup=True):
                    print("🎯 自毁式注入完成！")
                    print("📝 现在可以正常使用Augment，代码已恢复原状")
                    print("🗑️  所有痕迹已清除，完全自毁！")
                    return True
                else:
                    print("⚠️ 还原失败，但登录成功")
                    return False
                    
            time.sleep(3)  # 每3秒检查一次
            print(".", end="", flush=True)
            
        print(f"\n⏰ 等待超时 ({timeout}秒)")
        print("💡 如果已经登录，可以手动运行还原")
        return False
        
    def run(self):
        """执行完整的自毁式注入流程"""
        print("🎯 自毁式Token注入器")
        print("=" * 40)
        
        # 1. 查找插件
        if not self.find_plugin():
            return False
            
        # 2. 查找VSCode
        if not self.find_vscode():
            return False
            
        # 3. 注入token
        if not self.inject_token():
            return False
            
        # 4. 打开VSCode
        if not self.open_vscode():
            return False
            
        # 5. 等待登录并还原
        return self.wait_for_login_and_restore()

def main():
    # 配置token和租户URL
    token = "375d580f539201091ed620437f83d0bec18fd52dfd043a5ec8d750c906617eea"
    tenant_url = "https://d1.api.augmentcode.com/"
    
    injector = AutoInjectAndRestore(token, tenant_url)
    
    try:
        success = injector.run()
        if success:
            print("\n🎉 任务完成！")
        else:
            print("\n💥 任务失败！")
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断")
        print("💡 如果需要还原代码，请手动运行还原功能")

if __name__ == "__main__":
    main()
