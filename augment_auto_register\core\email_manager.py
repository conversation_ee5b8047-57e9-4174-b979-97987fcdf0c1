#!/usr/bin/env python3
"""
Augment 自动注册工具 - 邮箱管理模块
负责生成随机8位数字邮箱地址
"""

from typing import Optional
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.config_manager import get_config
from utils.helpers import generate_random_digits, validate_email_format
from utils.logger import get_logger


class EmailManager:
    """邮箱管理器"""
    
    def __init__(self, config_manager=None):
        """
        初始化邮箱管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager or get_config()
        self.logger = get_logger("EmailManager")
        
        # 获取邮箱配置
        self.email_suffix = self.config.get('email.suffix', '@example.com')
        
        self.logger.info(f"邮箱管理器初始化完成，邮箱后缀: {self.email_suffix}")
    
    def generate_email(self) -> str:
        """
        生成随机8位数字邮箱地址
        
        Returns:
            生成的邮箱地址
        """
        # 生成随机8位数字作为邮箱前缀
        random_prefix = generate_random_digits(8)
        
        # 组合完整邮箱地址
        email_address = f"{random_prefix}{self.email_suffix}"
        
        # 验证邮箱格式
        if not validate_email_format(email_address):
            self.logger.warning(f"生成的邮箱格式可能无效: {email_address}")
        
        self.logger.info(f"生成邮箱地址: {email_address}")
        return email_address
    
    def generate_multiple_emails(self, count: int) -> list[str]:
        """
        生成多个随机邮箱地址
        
        Args:
            count: 生成数量
            
        Returns:
            邮箱地址列表
        """
        emails = []
        for i in range(count):
            email = self.generate_email()
            emails.append(email)
        
        self.logger.info(f"生成了 {count} 个邮箱地址")
        return emails
    
    def validate_email(self, email: str) -> bool:
        """
        验证邮箱地址格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            是否为有效格式
        """
        is_valid = validate_email_format(email)
        
        if is_valid:
            self.logger.debug(f"邮箱格式有效: {email}")
        else:
            self.logger.warning(f"邮箱格式无效: {email}")
        
        return is_valid
    
    def is_generated_email(self, email: str) -> bool:
        """
        检查是否为本工具生成的邮箱
        
        Args:
            email: 邮箱地址
            
        Returns:
            是否为生成的邮箱
        """
        if not email.endswith(self.email_suffix):
            return False
        
        # 提取前缀部分
        prefix = email.replace(self.email_suffix, '')
        
        # 检查是否为8位数字
        if len(prefix) == 8 and prefix.isdigit():
            return True
        
        return False
    
    def get_email_prefix(self, email: str) -> Optional[str]:
        """
        从邮箱地址中提取前缀
        
        Args:
            email: 邮箱地址
            
        Returns:
            邮箱前缀，如果不是有效格式则返回None
        """
        if not email.endswith(self.email_suffix):
            return None
        
        prefix = email.replace(self.email_suffix, '')
        return prefix if prefix else None
    
    def get_email_suffix(self) -> str:
        """
        获取当前配置的邮箱后缀
        
        Returns:
            邮箱后缀
        """
        return self.email_suffix
    
    def set_email_suffix(self, suffix: str) -> None:
        """
        设置邮箱后缀（运行时修改）
        
        Args:
            suffix: 新的邮箱后缀
        """
        if not suffix.startswith('@'):
            suffix = '@' + suffix
        
        old_suffix = self.email_suffix
        self.email_suffix = suffix
        
        self.logger.info(f"邮箱后缀已更改: {old_suffix} -> {suffix}")
    
    def get_stats(self) -> dict:
        """
        获取邮箱管理器统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'email_suffix': self.email_suffix,
            'prefix_length': 8,
            'prefix_type': 'digits_only'
        }
    
    def __str__(self) -> str:
        """返回邮箱管理器的字符串表示"""
        return f"EmailManager(suffix={self.email_suffix})"
    
    def __repr__(self) -> str:
        return self.__str__()


if __name__ == "__main__":
    # 测试邮箱管理器
    from ..utils.logger import setup_logger
    
    # 设置日志
    setup_logger(level="DEBUG")
    
    # 创建邮箱管理器
    email_manager = EmailManager()
    
    print("🧪 测试邮箱管理器")
    
    # 测试生成单个邮箱
    email = email_manager.generate_email()
    print(f"生成的邮箱: {email}")
    
    # 测试邮箱验证
    is_valid = email_manager.validate_email(email)
    print(f"邮箱格式有效: {is_valid}")
    
    # 测试是否为生成的邮箱
    is_generated = email_manager.is_generated_email(email)
    print(f"是生成的邮箱: {is_generated}")
    
    # 测试提取前缀
    prefix = email_manager.get_email_prefix(email)
    print(f"邮箱前缀: {prefix}")
    
    # 测试生成多个邮箱
    emails = email_manager.generate_multiple_emails(3)
    print(f"生成的多个邮箱: {emails}")
    
    # 测试统计信息
    stats = email_manager.get_stats()
    print(f"统计信息: {stats}")
    
    print("✅ 邮箱管理器测试完成")
