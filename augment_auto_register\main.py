#!/usr/bin/env python3
"""
Augment 自动注册工具 - 主程序入口
提供用户友好的命令行界面，调用注册流程并处理结果
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 修复导入路径
import config.config_manager as config_manager
import utils.logger as logger_utils
import core.register_flow as register_flow


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Augment 自动注册工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 使用默认配置运行
  python main.py --config custom.yaml  # 使用自定义配置文件
  python main.py --debug           # 启用调试模式
  python main.py --headless        # 无头模式运行
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径 (默认: config/config.yaml)'
    )
    
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='无头模式运行浏览器'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--no-log-file',
        action='store_true',
        help='不保存日志到文件'
    )
    
    return parser.parse_args()


def setup_logging(args):
    """设置日志系统"""
    log_level = 'DEBUG' if args.debug else args.log_level
    save_to_file = not args.no_log_file

    logger_utils.setup_logger(
        level=log_level,
        save_to_file=save_to_file
    )

    return logger_utils.get_logger("Main")


def load_configuration(args) -> Optional[config_manager.ConfigManager]:
    """加载配置文件"""
    try:
        config = config_manager.get_config(args.config)
        
        # 如果命令行指定了无头模式，覆盖配置文件设置
        if args.headless:
            config.config_data['browser']['headless'] = True
        
        return config
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        print("请检查配置文件是否存在且格式正确")
        return None


def display_banner():
    """显示程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Augment 自动注册工具                        ║
║                                                              ║
║  🚀 自动化注册流程：邮箱生成 → 人机验证 → 验证码获取 → 完成注册    ║
║  📧 支持随机8位数字邮箱生成                                     ║
║  🤖 智能人机验证处理                                           ║
║  📬 QQ邮箱IMAP验证码获取                                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def display_config_info(config: config_manager.ConfigManager, logger):
    """显示配置信息"""
    logger.info("📋 当前配置信息:")
    logger.info(f"  邮箱后缀: {config.get('email.suffix')}")
    logger.info(f"  IMAP服务器: {config.get('email.imap.server')}")
    logger.info(f"  浏览器无头模式: {config.get('browser.headless')}")
    logger.info(f"  验证码等待时间: {config.get('registration.wait_timeout')}秒")
    logger.info(f"  人机验证等待时间: {config.get('registration.verification_wait')}秒")


def confirm_start(logger) -> bool:
    """确认开始注册"""
    try:
        logger.info("⚠️  请确认以下事项:")
        logger.info("  1. 已正确配置QQ邮箱IMAP信息")
        logger.info("  2. 已启用QQ邮箱的IMAP服务")
        logger.info("  3. 网络连接正常")
        logger.info("")
        
        response = input("确认开始自动注册? (y/N): ").strip().lower()
        return response in ['y', 'yes', '是']
        
    except KeyboardInterrupt:
        logger.info("\n用户取消操作")
        return False


def display_result(result: dict, logger):
    """显示注册结果"""
    if result and result.get('success'):
        logger.success("🎉 注册成功！")
        logger.info("📊 注册结果:")
        logger.info(f"  📧 注册邮箱: {result.get('email')}")
        logger.info(f"  🔢 验证码: {result.get('verification_code')}")
        logger.info(f"  ⏱️  耗时: {result.get('duration', 0):.1f}秒")
        logger.info(f"  🌐 最终URL: {result.get('final_url')}")
        
        if result.get('result_file'):
            logger.info(f"  💾 详细结果已保存到: {result.get('result_file')}")
        
        logger.info("")
        logger.info("✅ 注册流程已完成，请检查邮箱确认注册成功")
        
    else:
        logger.error("❌ 注册失败")
        logger.info("💡 建议检查:")
        logger.info("  1. 网络连接是否正常")
        logger.info("  2. 邮箱配置是否正确")
        logger.info("  3. 查看日志文件了解详细错误信息")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 显示横幅
    display_banner()
    
    # 设置日志
    logger = setup_logging(args)
    
    try:
        logger.info("🚀 启动Augment自动注册工具")
        
        # 加载配置
        config = load_configuration(args)
        if not config:
            sys.exit(1)
        
        # 显示配置信息
        display_config_info(config, logger)
        
        # 确认开始
        if not confirm_start(logger):
            logger.info("用户取消操作，程序退出")
            sys.exit(0)
        
        # 创建注册器并运行
        logger.info("🔧 初始化注册器...")
        register = register_flow.AugmentAutoRegister(config)
        
        logger.info("🎯 开始执行注册流程...")
        result = register.run_registration()
        
        # 显示结果
        display_result(result, logger)
        
        # 根据结果设置退出码
        if result and result.get('success'):
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.warning("\n⚠️ 用户中断操作")
        sys.exit(130)
        
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        if args.debug:
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
