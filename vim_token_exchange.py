#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vim Augment插件Token交换器
模拟Vim插件的token交换逻辑
"""

import requests
import json
import uuid
import sys

class VimTokenExchanger:
    """
    模拟Vim插件的token交换逻辑
    基于对augment.vim插件的分析
    """
    
    def __init__(self):
        # Vim插件使用的client_id（经过测试验证）
        self.client_id = "v"  # 唯一有效的client_id

        # Vim插件使用空字符串作为redirect_uri（经过测试验证）
        self.redirect_uri = ""  # 空字符串 - 唯一有效的配置
    
    def generate_vim_headers(self):
        """
        生成Vim插件风格的请求头
        """
        request_id = str(uuid.uuid4())
        session_id = str(uuid.uuid4())
        
        return {
            "Content-Type": "application/json",
            "User-Agent": "Augment-Vim/0.25.1",  # 使用Vim插件的User-Agent
            "Accept": "application/json",
            "x-request-id": request_id,
            "x-request-session-id": session_id,
        }
    
    def try_vim_token_exchange(self, code, tenant_url, code_verifier, state=None):
        """
        按照Vim插件的方式交换token（使用已验证的配置）
        """
        if not tenant_url.endswith('/'):
            tenant_url += '/'

        token_url = f"{tenant_url}token"

        print(f"🔄 向 {token_url} 交换token...")
        print(f"📝 Code: {code[:20]}...")
        print(f"🔐 Code Verifier: {code_verifier[:20]}...")
        print(f"🏢 使用 client_id: '{self.client_id}'")

        # 构建payload（使用已验证有效的配置）
        payload = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "code": code,
            "code_verifier": code_verifier,
            "redirect_uri": self.redirect_uri
        }

        # 添加state参数（如果有）
        if state:
            payload["state"] = state

        # 交换token
        result = self._make_token_request(token_url, payload)
        if result:
            print(f"✅ Token交换成功!")
            return result
        else:
            print("❌ Token交换失败")
            return None
    
    def _make_token_request(self, token_url, payload):
        """
        发送token交换请求
        """
        headers = self.generate_vim_headers()
        
        try:
            # 使用JSON格式发送请求（已验证有效）
            response = requests.post(token_url, json=payload, headers=headers, timeout=30)
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                token_data = response.json()
                return token_data
            else:
                print(f"❌ 失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📄 错误详情: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"📄 响应内容: {response.text}")
                
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def print_token_info(self, token_data):
        """
        打印token信息
        """
        print("\n🎉 Token交换成功!")
        print("=" * 40)
        print(json.dumps(token_data, indent=2))
        
        if 'access_token' in token_data:
            print(f"\n🔑 Access Token: {token_data['access_token']}")
            print(f"⏰ Expires In: {token_data.get('expires_in', 'N/A')}")
            print(f"🏷️  Token Type: {token_data.get('token_type', 'Bearer')}")
            
            if 'refresh_token' in token_data:
                print(f"🔄 Refresh Token: {token_data['refresh_token']}")

def main():
    if len(sys.argv) < 4:
        print("使用方法:")
        print("python vim_token_exchange.py <code> <tenant_url> <code_verifier> [state]")
        print()
        print("参数说明:")
        print("  code         - OAuth授权码")
        print("  tenant_url   - 租户URL (如: https://d16.api.augmentcode.com/)")
        print("  code_verifier - PKCE验证码")
        print("  state        - 可选的state参数")
        print()
        print("示例:")
        print("python vim_token_exchange.py abc123 https://d16.api.augmentcode.com/ xyz789")
        return
    
    code = sys.argv[1]
    tenant_url = sys.argv[2]
    code_verifier = sys.argv[3]
    state = sys.argv[4] if len(sys.argv) > 4 else None
    
    print("🚀 Vim Augment插件Token交换器")
    print("=" * 40)
    
    exchanger = VimTokenExchanger()
    token_data = exchanger.try_vim_token_exchange(code, tenant_url, code_verifier, state)
    
    if token_data:
        exchanger.print_token_info(token_data)
    else:
        print("\n❌ Token交换失败")
        print("请检查:")
        print("1. 授权码是否正确且未过期")
        print("2. tenant_url是否正确")
        print("3. code_verifier是否与生成URL时使用的一致")
        print("4. 网络连接是否正常")

if __name__ == "__main__":
    main()
