# Augment 自动注册工具

🚀 **全自动化的 Augment Code 账号注册工具**

## ✨ 功能特性

- 🎯 **完全自动化**: 三步注册流程全自动完成
- 📧 **智能邮箱生成**: 随机8位数字邮箱地址
- 🤖 **智能人机验证**: 自动检测验证状态，无需手动操作
- 📬 **验证码自动获取**: 通过IMAP连接QQ邮箱获取验证码
- 🔧 **模块化设计**: 易于扩展和维护
- 📊 **详细日志**: 彩色控制台输出和文件日志
- ⚙️ **灵活配置**: YAML配置文件，支持多种自定义选项

## 📋 注册流程

1. **步骤1**: 邮箱输入 + 人机验证
   - 生成随机8位数字邮箱
   - 自动填写邮箱地址
   - 智能处理人机验证（自动检测Success状态）
   - 点击Continue按钮

2. **步骤2**: 验证码获取 + 输入
   - 通过IMAP连接QQ邮箱
   - 实时监听新邮件
   - 自动提取6位数字验证码
   - 填写验证码并提交

3. **步骤3**: 完成注册
   - 处理条款同意（如需要）
   - 点击最终注册按钮
   - 验证注册成功

## 🛠️ 安装说明

### 1. 环境要求

- Python 3.8+
- Chrome 浏览器
- QQ邮箱（已开启IMAP服务）

### 2. 安装依赖

```bash
# 克隆或下载项目
cd augment_auto_register

# 安装依赖包
pip install -r requirements.txt
```

### 3. 依赖包说明

- `DrissionPage`: 浏览器自动化框架
- `colorama`: 彩色终端输出
- `requests`: HTTP请求库
- `PyYAML`: YAML配置文件解析
- `loguru`: 高级日志处理

## ⚙️ 配置指南

### 1. 配置文件位置

配置文件位于 `config/config.yaml`

### 2. 邮箱配置

```yaml
email:
  # 邮箱后缀（随机8位数字 + 后缀）
  suffix: "@example.com"
  
  # IMAP邮箱服务配置（用于接收验证码）
  imap:
    server: "imap.qq.com"
    port: 993
    username: "<EMAIL>"        # 你的QQ邮箱
    password: "your_app_password"     # QQ邮箱应用密码
    use_ssl: true
```

### 3. QQ邮箱设置

1. **开启IMAP服务**:
   - 登录QQ邮箱网页版
   - 设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务
   - 开启IMAP/SMTP服务

2. **获取应用密码**:
   - 在上述页面生成授权码
   - 将授权码填入配置文件的 `password` 字段

### 4. 浏览器配置

```yaml
browser:
  headless: false          # 是否无头模式
  timeout: 30             # 页面加载超时时间
  chrome_path: ""         # Chrome路径（留空自动检测）
```

### 5. 注册流程配置

```yaml
registration:
  wait_timeout: 90        # 等待验证码的最大时间（秒）
  verification_wait: 15   # 人机验证最大等待时间（秒）
  max_retries: 3         # 最大重试次数
  page_load_wait: 2      # 页面加载等待时间（秒）
```

## 🚀 使用方法

### 1. 基本使用

```bash
# 使用默认配置运行
python main.py
```

### 2. 命令行选项

```bash
# 使用自定义配置文件
python main.py --config custom.yaml

# 启用调试模式
python main.py --debug

# 无头模式运行
python main.py --headless

# 设置日志级别
python main.py --log-level DEBUG

# 不保存日志文件
python main.py --no-log-file
```

### 3. 运行示例

```bash
# 完整的调试运行
python main.py --debug --log-level DEBUG

# 静默运行（无头模式）
python main.py --headless --log-level WARNING
```

## 📊 输出结果

### 1. 控制台输出

程序会显示彩色的实时日志，包括：
- 🚀 启动信息
- 📧 邮箱生成
- 🤖 人机验证状态
- 📬 验证码获取
- ✅ 注册成功信息

### 2. 结果文件

注册成功后会生成JSON结果文件：

```json
{
  "success": true,
  "email": "<EMAIL>",
  "verification_code": "123456",
  "duration": 45.2,
  "timestamp": 1703123456.789,
  "final_url": "https://app.augmentcode.com/dashboard",
  "message": "注册成功完成"
}
```

### 3. 日志文件

详细日志保存在 `logs/augment_register.log`

## 🔧 项目结构

```
augment_auto_register/
├── config/
│   ├── config.yaml          # 配置文件
│   └── config_manager.py    # 配置管理器
├── core/
│   ├── browser_manager.py   # 浏览器管理
│   ├── email_manager.py     # 邮箱管理
│   ├── human_verification.py # 人机验证处理
│   ├── verification_code.py # 验证码获取
│   └── register_flow.py     # 注册流程核心
├── utils/
│   ├── logger.py           # 日志管理
│   └── helpers.py          # 工具函数
├── logs/                   # 日志文件目录
├── results/               # 结果文件目录
├── main.py               # 主程序入口
├── requirements.txt      # 依赖列表
└── README.md            # 项目文档
```

## ❓ 常见问题

### Q1: 邮箱连接失败怎么办？

**A**: 检查以下几点：
1. 确认QQ邮箱已开启IMAP服务
2. 使用应用密码而不是QQ密码
3. 检查网络连接
4. 确认配置文件中的邮箱信息正确

### Q2: 人机验证一直失败？

**A**: 
1. 人机验证是自动检测的，无需手动操作
2. 如果一直显示"verifying"，请等待更长时间
3. 检查网络连接是否稳定

### Q3: 验证码获取超时？

**A**:
1. 检查邮箱配置是否正确
2. 确认邮箱能正常接收邮件
3. 增加 `wait_timeout` 配置值
4. 检查邮箱是否有垃圾邮件过滤

### Q4: Chrome浏览器找不到？

**A**:
1. 确认已安装Chrome浏览器
2. 在配置文件中指定Chrome路径
3. 检查Chrome是否为最新版本

### Q5: 如何查看详细错误信息？

**A**:
```bash
# 启用调试模式查看详细信息
python main.py --debug --log-level DEBUG
```

## 🔄 扩展功能

### 1. 支持其他邮箱服务

修改 `core/verification_code.py` 中的IMAP配置

### 2. 添加代理支持

在 `core/browser_manager.py` 中添加代理配置

### 3. 批量注册

基于现有模块可以轻松实现批量注册功能

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🚀 完整的三步注册流程
- 📧 随机邮箱生成
- 🤖 智能人机验证
- 📬 自动验证码获取

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**⚠️ 免责声明**: 本工具仅供学习和研究目的，使用者需自行承担使用风险，并遵守相关网站的服务条款。
