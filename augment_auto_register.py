#!/usr/bin/env python3
"""
AugmentCode 完全自动注册脚本
基于 Cursor 自动注册方案，适配 AugmentCode 注册流程
只需要邮箱和验证码，无需姓名和密码
"""

import time
import random
import json
import os
import sys
import imaplib
import email
import re
import configparser
from DrissionPage import ChromiumOptions, ChromiumPage
from colorama import Fore, Style, init

# 初始化 colorama
init(autoreset=True)

class AugmentAutoRegister:
    def __init__(self, config_file='config.ini'):
        self.page = None
        self.config = self.load_config(config_file)
        
    def load_config(self, config_file):
        """加载配置文件"""
        config = configparser.ConfigParser()

        # 如果配置文件不存在，直接退出
        if not os.path.exists(config_file):
            print(f"{Fore.RED}❌ 配置文件不存在: {config_file}")
            print(f"{Fore.YELLOW}请先创建配置文件并填写正确的邮箱信息")
            sys.exit(1)

        config.read(config_file, encoding='utf-8')

        # 验证必要的配置项
        required_sections = ['email', 'browser']
        for section in required_sections:
            if not config.has_section(section):
                print(f"{Fore.RED}❌ 配置文件缺少 [{section}] 部分")
                sys.exit(1)

        return config
    
    def generate_email(self):
        """生成随机邮箱地址"""
        # 生成随机用户名
        chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        username = ''.join(random.choices(chars, k=8))
        
        # 从配置读取域名
        domain = self.config.get('email', 'email_domain')
        
        return f"{username}@{domain}"
    
    def get_random_wait_time(self, timing_type='page_load_wait'):
        """获取随机等待时间"""
        timing_config = {
            'page_load_wait': (0.1, 0.8),
            'input_wait': (0.3, 0.8),
            'submit_wait': (0.5, 1.5),
            'verification_code_input': (0.1, 0.3),
            'verification_success_wait': (2, 3),
            'verification_retry_wait': (2, 3),
            'auth_wait': (1, 2)
        }

        min_time, max_time = timing_config.get(timing_type, (0.1, 0.8))
        return random.uniform(min_time, max_time)

    def setup_driver(self):
        """设置浏览器"""
        print(f"{Fore.CYAN}启动浏览器...")

        try:
            # 获取浏览器路径
            chrome_path = self.config.get('browser', 'chrome_path', fallback='')
            if not chrome_path:
                # 自动查找 Chrome 路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', ''))
                ]

                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_path = path
                        break

                if not chrome_path:
                    chrome_path = chrome_paths[0]  # 使用默认路径

            # 设置浏览器选项
            co = ChromiumOptions()
            co.set_browser_path(chrome_path)
            # co.set_argument("--incognito")  # 移除无痕模式，以便正确获取session cookies

            # 只在 Linux 上添加 --no-sandbox
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            co.auto_port()

            # 设置是否无头模式
            headless = self.config.getboolean('browser', 'headless', fallback=False)
            co.headless(headless)

            print(f"{Fore.CYAN}使用浏览器: {chrome_path}")

            # 启动浏览器
            self.page = ChromiumPage(co)
            time.sleep(1)

            print(f"{Fore.GREEN}✅ 浏览器启动成功")
            return True

        except Exception as e:
            print(f"{Fore.RED}❌ 浏览器启动失败: {e}")
            return False

    def simulate_human_input(self, url):
        """模拟人类输入"""
        print(f"{Fore.CYAN}访问页面: {url}")

        # 关键步骤：先访问空白页
        self.page.get('about:blank')
        time.sleep(self.get_random_wait_time('page_load_wait'))

        # 访问目标页面
        self.page.get(url)
        time.sleep(self.get_random_wait_time('page_load_wait'))

    def navigate_to_auth_page(self):
        """导航到Auth0注册页面"""
        try:
            print(f"{Fore.CYAN}导航到注册页面...")

            # 直接访问注册端点，让它自动重定向到登录页面
            self.simulate_human_input('https://app.augmentcode.com')
            time.sleep(3)
            
            # 等待重定向到Auth0页面
            max_attempts = 15
            for attempt in range(max_attempts):
                current_url = self.page.url
                print(f"{Fore.CYAN}当前URL: {current_url}")
                
                # 检查是否已经到达Auth0登录页面
                if 'login.augmentcode.com' in current_url and 'identifier' in current_url:
                    print(f"{Fore.GREEN}✅ 已到达Auth0登录页面")
                    return True
                
                # 如果还在主页面，等待自动重定向
                if 'augmentcode.com' in current_url and 'login.augmentcode.com' not in current_url:
                    print(f"{Fore.CYAN}等待重定向到Auth0页面...")
                    time.sleep(2)
                    continue
                
                time.sleep(1)
            
            print(f"{Fore.YELLOW}⚠️ 未能自动重定向到Auth0页面，当前URL: {self.page.url}")
            return True  # 继续尝试，可能页面已经正确加载
            
        except Exception as e:
            print(f"{Fore.RED}❌ 导航到Auth0页面失败: {e}")
            return False

    def fill_email_form(self, email):
        """填写邮箱表单"""
        print(f"{Fore.CYAN}填写邮箱: {email}")

        try:
            # 等待页面加载更长时间
            print(f"{Fore.CYAN}等待页面完全加载...")
            time.sleep(5)

            # 根据实际页面元素，查找邮箱输入框
            # <input class="input c04aa24a5 cf229b4de" inputmode="email" name="username" id="username" type="text" value="" required="" autocomplete="email" autocapitalize="none" spellcheck="false" autofocus="">
            email_selectors = [
                "@name=username",  # 主要选择器，根据实际元素
                "@id=username",    # 备用选择器
                "@inputmode=email", # 根据inputmode属性
                "@autocomplete=email", # 根据autocomplete属性
                "input[name='username']",
                "input[id='username']",
                "input[inputmode='email']",
                "input[autocomplete='email']",
                "input[autofocus]",  # 有autofocus属性
                "input[type='text'][autocomplete='email']",  # 组合选择器
                "input[type='text'][name='username']",
                # 备用选择器
                "@type=email",
                "@name=email",
                "@id=email",
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                # 通用选择器
                "input[type='text']",  # 最后的备用选择器
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.page.ele(selector, timeout=2)
                    if email_input:
                        print(f"{Fore.GREEN}找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                print(f"{Fore.RED}❌ 未找到邮箱输入框")
                # 打印页面信息用于调试
                print(f"{Fore.YELLOW}当前页面URL: {self.page.url}")
                print(f"{Fore.YELLOW}页面标题: {self.page.title}")

                # 查找所有输入框进行调试
                print(f"{Fore.YELLOW}页面中的所有输入框:")
                try:
                    all_inputs = self.page.eles("tag:input")
                    for i, inp in enumerate(all_inputs[:10]):  # 只显示前10个
                        input_type = inp.attr('type') or 'unknown'
                        input_name = inp.attr('name') or 'unknown'
                        input_id = inp.attr('id') or 'unknown'
                        input_placeholder = inp.attr('placeholder') or 'unknown'
                        input_class = inp.attr('class') or 'unknown'
                        input_inputmode = inp.attr('inputmode') or 'unknown'
                        print(f"  输入框{i+1}: type={input_type}, name={input_name}, id={input_id}")
                        print(f"           placeholder={input_placeholder}")
                        print(f"           class={input_class[:50]}...")
                        print(f"           inputmode={input_inputmode}")
                except Exception as e:
                    print(f"{Fore.YELLOW}获取输入框信息失败: {e}")

                return False

            # 填写邮箱
            email_input.clear()
            time.sleep(0.3)
            email_input.input(email)
            time.sleep(self.get_random_wait_time('input_wait'))

            # 等待验证成功提示（Success!）
            print(f"{Fore.CYAN}等待邮箱验证...")
            time.sleep(2)

            # 检查是否出现Success提示
            success_indicators = [
                "text:Success!",
                "text:✓",
                "xpath://div[contains(text(), 'Success')]",
                "xpath://*[contains(@class, 'success')]"
            ]

            success_found = False
            for indicator in success_indicators:
                try:
                    success_element = self.page.ele(indicator, timeout=1)
                    if success_element:
                        print(f"{Fore.GREEN}✅ 邮箱验证成功提示: {indicator}")
                        success_found = True
                        break
                except:
                    continue

            if not success_found:
                print(f"{Fore.YELLOW}⚠️ 未找到Success提示，继续执行...")

            # 查找Continue按钮
            # <button type="submit" name="action" value="default" class="c8ccf545f cd30107ea cb74f79c9 cba4da699 _button-login-id" data-action-button-primary="true">Continue</button>
            continue_selectors = [
                "@type=submit",  # 主要选择器
                "@name=action",  # 根据实际元素
                "@data-action-button-primary=true",  # 根据data属性
                "button[type='submit']",
                "button[name='action']",
                "button[data-action-button-primary='true']",
                "button[value='default']",
                "text:Continue",
                "button:Continue",
                "input[type='submit']",
                "xpath://button[contains(text(), 'Continue')]",
                "xpath://button[contains(text(), 'Next')]",
                "xpath://button[@type='submit']",
                "xpath://button[@name='action']"
            ]

            continue_button = None
            for selector in continue_selectors:
                try:
                    continue_button = self.page.ele(selector, timeout=2)
                    if continue_button:
                        print(f"{Fore.GREEN}找到Continue按钮: {selector}")
                        break
                except:
                    continue

            if continue_button:
                # 确保按钮可点击，等待更长时间
                print(f"{Fore.CYAN}等待按钮可点击...")
                time.sleep(2)

                # 尝试点击按钮（只点击一次）
                try:
                    print(f"{Fore.GREEN}开始点击Continue按钮...")
                    continue_button.click()
                    time.sleep(self.get_random_wait_time('submit_wait'))
                    print(f"{Fore.GREEN}✅ 邮箱表单提交成功")
                    return True
                except Exception as e:
                    print(f"{Fore.YELLOW}点击按钮失败: {e}，尝试按回车键")
                    email_input.input('\n')
                    time.sleep(self.get_random_wait_time('submit_wait'))
                    return True
            else:
                print(f"{Fore.YELLOW}⚠️ 未找到Continue按钮")

                # 查找所有按钮进行调试
                print(f"{Fore.YELLOW}页面中的所有按钮:")
                try:
                    all_buttons = self.page.eles("tag:button")
                    for i, btn in enumerate(all_buttons[:5]):  # 只显示前5个
                        btn_type = btn.attr('type') or 'unknown'
                        btn_name = btn.attr('name') or 'unknown'
                        btn_class = btn.attr('class') or 'unknown'
                        btn_text = btn.text or 'unknown'
                        btn_value = btn.attr('value') or 'unknown'
                        print(f"  按钮{i+1}: type={btn_type}, name={btn_name}, value={btn_value}")
                        print(f"         text={btn_text}, class={btn_class[:50]}...")
                except Exception as e:
                    print(f"{Fore.YELLOW}获取按钮信息失败: {e}")

                print(f"{Fore.YELLOW}尝试按回车键提交...")
                email_input.input('\n')
                time.sleep(self.get_random_wait_time('submit_wait'))
                return True

        except Exception as e:
            print(f"{Fore.RED}❌ 邮箱表单填写失败: {e}")
            import traceback
            print(f"{Fore.RED}详细错误: {traceback.format_exc()}")
            return False

    def get_verification_code_from_email(self, target_email, max_wait=60):
        """从邮箱获取验证码"""
        print(f"{Fore.CYAN}开始获取验证码，目标邮箱: {target_email}")

        try:
            # 连接到邮箱服务器
            imap_server = self.config.get('email', 'imap_server')
            imap_port = self.config.getint('email', 'imap_port')
            use_ssl = self.config.getboolean('email', 'use_ssl')
            email_address = self.config.get('email', 'email_address')
            email_password = self.config.get('email', 'email_password')

            if use_ssl:
                mail = imaplib.IMAP4_SSL(imap_server, imap_port)
            else:
                mail = imaplib.IMAP4(imap_server, imap_port)

            mail.login(email_address, email_password)
            mail.select('inbox')

            print(f"{Fore.CYAN}已连接到邮箱服务器，记录当前邮件数量...")

            # 获取当前邮件数量作为基准
            status, initial_messages = mail.search(None, 'ALL')
            initial_count = len(initial_messages[0].split()) if initial_messages[0] else 0
            print(f"{Fore.CYAN}当前邮箱中有 {initial_count} 封邮件，等待新邮件...")

            start_time = time.time()
            check_interval = 3  # 每3秒检查一次

            while time.time() - start_time < max_wait:
                try:
                    # 搜索所有邮件
                    status, messages = mail.search(None, 'ALL')

                    if status == 'OK' and messages[0]:
                        # 获取所有邮件ID
                        email_ids = messages[0].split()
                        current_count = len(email_ids)

                        # 检查是否有新邮件
                        if current_count > initial_count:
                            print(f"{Fore.GREEN}发现新邮件！当前 {current_count} 封，之前 {initial_count} 封")

                            # 只检查新邮件
                            new_emails = email_ids[initial_count:]

                            for email_id in new_emails:
                                try:
                                    # 获取邮件内容
                                    status, msg_data = mail.fetch(email_id, '(RFC822)')

                                    if status == 'OK':
                                        email_body = msg_data[0][1]
                                        email_message = email.message_from_bytes(email_body)

                                        # 获取发件人、主题和收件人
                                        sender = email_message.get('From', '')
                                        subject = email_message.get('Subject', '')
                                        to_address = email_message.get('To', '')

                                        print(f"{Fore.CYAN}检查新邮件 - 发件人: {sender}")
                                        print(f"{Fore.CYAN}主题: {subject}")
                                        print(f"{Fore.CYAN}收件人: {to_address}")

                                        # 检查是否是 AugmentCode 相关邮件（根据实际邮件格式）
                                        is_augment_email = False

                                        # 首先检查邮件是否发送给目标邮箱
                                        is_target_email = target_email.lower() in to_address.lower()

                                        if is_target_email:
                                            print(f"{Fore.GREEN}✅ 邮件发送给目标邮箱: {target_email}")

                                            # 根据截图，AugmentCode邮件特征：
                                            # 发件人：Augment Sign Up
                                            # 主题：Welcome to Augment Sign Up

                                            # 检查发件人
                                            if any(keyword in sender.lower() for keyword in ['augment sign up', 'augment']):
                                                is_augment_email = True

                                            # 检查主题
                                            if any(keyword in subject.lower() for keyword in ['welcome to augment', 'augment sign up']):
                                                is_augment_email = True

                                            # 排除Cursor邮件
                                            if any(keyword in sender.lower() for keyword in ['cursor', 'cursor.sh']):
                                                is_augment_email = False
                                            if any(keyword in subject.lower() for keyword in ['cursor', 'verify your email address']):
                                                is_augment_email = False
                                        else:
                                            print(f"{Fore.YELLOW}跳过邮件：不是发送给目标邮箱 {target_email}")

                                        if is_augment_email:

                                            print(f"{Fore.GREEN}✅ 找到 AugmentCode 验证码邮件！")

                                            # 获取邮件内容
                                            content = self.get_email_content(email_message)
                                            print(f"{Fore.CYAN}邮件内容预览: {content[:300]}...")

                                            # 提取验证码
                                            verification_code = self.extract_verification_code(content)

                                            if verification_code:
                                                print(f"{Fore.GREEN}✅ 成功获取验证码: {verification_code}")

                                                # 不删除邮件，保留作为记录
                                                mail.close()
                                                mail.logout()
                                                return verification_code
                                            else:
                                                print(f"{Fore.YELLOW}⚠️ 邮件中未找到6位验证码")
                                        else:
                                            print(f"{Fore.YELLOW}跳过非 AugmentCode 邮件")

                                except Exception as e:
                                    print(f"{Fore.RED}处理邮件 {email_id} 时出错: {e}")
                                    continue

                            # 更新基准邮件数量
                            initial_count = current_count
                        else:
                            print(f"{Fore.YELLOW}等待新邮件... ({int(time.time() - start_time)}/{max_wait}s)")

                    time.sleep(check_interval)

                except Exception as e:
                    print(f"{Fore.RED}检查邮件时出错: {e}")
                    time.sleep(check_interval)

            mail.close()
            mail.logout()
            print(f"{Fore.RED}❌ 获取验证码超时")
            return None

        except Exception as e:
            print(f"{Fore.RED}❌ 邮箱连接失败: {e}")
            return None

    def get_email_content(self, email_message):
        """获取邮件内容"""
        content = ""

        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif part.get_content_type() == "text/html":
                    content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')

        return content

    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        print(f"{Fore.CYAN}开始提取验证码，邮件内容长度: {len(content)}")

        # 根据截图，AugmentCode的验证码格式是：
        # "Your verification code is: 226317"

        # 优先使用AugmentCode特定的模式
        augment_patterns = [
            r'Your verification code is:\s*(\d{6})',       # Your verification code is: 226317
            r'verification code is:\s*(\d{6})',            # verification code is: 226317
            r'code is:\s*(\d{6})',                         # code is: 226317
        ]

        # 尝试AugmentCode特定模式
        for i, pattern in enumerate(augment_patterns):
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                print(f"{Fore.CYAN}AugmentCode模式 {i+1} '{pattern}' 找到: {matches}")
                for match in matches:
                    code = match if isinstance(match, str) else match[0]
                    if code and len(code) == 6 and code.isdigit():
                        print(f"{Fore.GREEN}✅ 找到AugmentCode验证码: {code}")
                        return code

        # 如果没找到，尝试通用模式
        general_patterns = [
            r'verification code[:\s]*([0-9]{6})',          # verification code: 123456
            r'verify[:\s]*([0-9]{6})',                     # verify: 123456
            r'code[:\s]*([0-9]{6})',                       # code: 123456
            r'Your code[:\s]*([0-9]{6})',                  # Your code: 123456
        ]

        for i, pattern in enumerate(general_patterns):
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                print(f"{Fore.CYAN}通用模式 {i+1} '{pattern}' 找到: {matches}")
                for match in matches:
                    code = match if isinstance(match, str) else match[0]
                    if code and len(code) == 6 and code.isdigit():
                        print(f"{Fore.GREEN}✅ 找到验证码: {code}")
                        return code

        # 最后尝试查找所有6位数字（但要更谨慎）
        all_6_digits = re.findall(r'\b(\d{6})\b', content)
        print(f"{Fore.CYAN}找到的所有6位数字: {all_6_digits}")

        # 过滤掉明显不是验证码的数字
        for code in all_6_digits:
            # 排除常见的非验证码数字
            if not any(bad in code for bad in ['2024', '2025', '0000', '1111', '2222', '123456', '000000']):
                # 进一步检查：验证码通常不会是连续数字或重复数字
                if not (code == code[0] * 6 or code in ['123456', '654321', '111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999']):
                    print(f"{Fore.GREEN}✅ 找到可能的验证码: {code}")
                    return code

        # 如果还是没找到，显示邮件内容用于调试
        print(f"{Fore.RED}❌ 未找到验证码")
        print(f"{Fore.YELLOW}邮件内容（用于调试）:")

        # 显示包含数字的行
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if re.search(r'\d', line):  # 包含数字的行
                print(f"{Fore.YELLOW}第{i+1}行: {line.strip()}")

        return None

    def handle_verification_code_auto(self, target_email):
        """自动处理验证码"""
        print(f"{Fore.CYAN}等待验证码页面...")

        try:
            # 等待页面跳转到验证码页面
            time.sleep(3)

            # 检查当前页面状态
            current_url = self.page.url
            print(f"{Fore.CYAN}当前页面URL: {current_url}")

            # 等待验证码输入框出现
            max_wait_attempts = 15
            verification_inputs = []

            for attempt in range(max_wait_attempts):
                print(f"{Fore.CYAN}查找验证码输入框... (尝试 {attempt + 1}/{max_wait_attempts})")

                # AugmentCode/Auth0 可能的验证码输入框选择器
                verification_selectors = [
                    "@name=code",           # Auth0常用
                    "@id=code",
                    "@type=text",
                    "@placeholder=Code",
                    "@placeholder=Enter code",
                    "@placeholder=Verification code",
                    "input[name='code']",
                    "input[id='code']",
                    "input[placeholder*='code']",
                    "input[placeholder*='Code']",
                    "input[maxlength='6']",     # 6位验证码
                    "input[pattern='[0-9]*']",  # 数字输入框
                ]

                # 首先尝试查找单个验证码输入框（Auth0常用模式）
                verification_input = None
                for selector in verification_selectors:
                    try:
                        verification_input = self.page.ele(selector, timeout=1)
                        if verification_input:
                            verification_inputs = [verification_input]
                            print(f"{Fore.GREEN}找到验证码输入框: {selector}")
                            break
                    except:
                        continue

                # 如果没找到单个输入框，尝试查找分离式输入框
                if not verification_inputs:
                    for i in range(6):
                        try:
                            input_element = self.page.ele(f"@data-index={i}", timeout=0.5)
                            if input_element:
                                verification_inputs.append(input_element)
                        except:
                            break

                    if verification_inputs:
                        print(f"{Fore.GREEN}找到分离式验证码输入框: {len(verification_inputs)} 个")

                if verification_inputs:
                    print(f"{Fore.GREEN}✅ 找到验证码输入框 ({len(verification_inputs)} 个)")
                    break

                print(f"{Fore.YELLOW}验证码输入框未找到，等待 2 秒...")
                time.sleep(2)

            if not verification_inputs:
                print(f"{Fore.RED}❌ 验证码输入框查找超时")
                print(f"{Fore.YELLOW}当前页面信息:")
                print(f"  URL: {self.page.url}")
                print(f"  标题: {self.page.title}")
                return False

            # 自动获取验证码
            wait_timeout = self.config.getint('registration', 'wait_timeout', fallback=90)
            verification_code = self.get_verification_code_from_email(target_email, wait_timeout)

            if not verification_code:
                print(f"{Fore.RED}❌ 未能获取到验证码")
                return False

            # 填写验证码
            print(f"{Fore.CYAN}填写验证码: {verification_code}")

            if len(verification_inputs) == 1:
                # 单个输入框（Auth0常用模式）
                try:
                    verification_inputs[0].clear()
                    time.sleep(0.2)
                    verification_inputs[0].input(verification_code)
                    print(f"  输入完整验证码: {verification_code}")

                    # 检查是否需要点击提交按钮
                    time.sleep(1)
                    submit_selectors = [
                        "text:Continue",
                        "text:Verify",
                        "text:Submit",
                        "@type=submit",
                        "button[type='submit']"
                    ]

                    submit_button = None
                    for selector in submit_selectors:
                        try:
                            submit_button = self.page.ele(selector, timeout=1)
                            if submit_button:
                                print(f"{Fore.GREEN}找到提交按钮: {selector}")
                                submit_button.click()
                                break
                        except:
                            continue

                    if not submit_button:
                        # 如果没有提交按钮，尝试按回车
                        verification_inputs[0].input('\n')

                    time.sleep(self.get_random_wait_time('verification_success_wait'))

                except Exception as e:
                    print(f"{Fore.RED}输入验证码失败: {e}")
                    return False
            else:
                # 多个输入框（分离式）
                for i, digit in enumerate(verification_code):
                    if i < len(verification_inputs):
                        try:
                            verification_inputs[i].clear()
                            time.sleep(0.1)
                            verification_inputs[i].input(digit)
                            print(f"  输入第 {i+1} 位: {digit}")
                            time.sleep(self.get_random_wait_time('verification_code_input'))
                        except Exception as e:
                            print(f"{Fore.RED}输入第 {i+1} 位数字失败: {e}")

            print(f"{Fore.GREEN}✅ 验证码填写完成")

            # 等待页面跳转或验证完成
            print(f"{Fore.CYAN}等待验证完成...")
            time.sleep(5)

            return True

        except Exception as e:
            print(f"{Fore.RED}❌ 验证码处理失败: {e}")
            import traceback
            print(f"{Fore.RED}详细错误: {traceback.format_exc()}")
            return False

    def check_login_status_and_get_session(self):
        """检查登录状态并获取session cookie"""
        try:
            print(f"{Fore.CYAN}检查登录状态...")

            # 访问需要登录的页面来检查状态
            subscription_url = "https://app.augmentcode.com/account/subscription"
            print(f"{Fore.CYAN}访问订阅页面: {subscription_url}")

            self.page.get(subscription_url)
            time.sleep(5)  # 等待页面加载和可能的重定向

            current_url = self.page.url
            print(f"{Fore.CYAN}当前页面URL: {current_url}")

            # 检查是否成功登录
            is_logged_in = False
            if "login" in current_url.lower():
                print(f"{Fore.YELLOW}⚠️ 被重定向到登录页面，可能需要重新登录")
                is_logged_in = False
            elif "account" in current_url.lower() or "subscription" in current_url.lower():
                print(f"{Fore.GREEN}✅ 已成功登录，在账户页面")
                is_logged_in = True
            else:
                print(f"{Fore.YELLOW}⚠️ 未知页面状态: {current_url}")
                # 尝试检查页面内容来判断登录状态
                try:
                    # 查找登录相关的元素
                    login_indicators = [
                        "text:Sign in",
                        "text:Log in",
                        "text:Login",
                        "text:Sign up",
                        "@name=username",
                        "@name=email",
                        "@type=password"
                    ]

                    for indicator in login_indicators:
                        try:
                            element = self.page.ele(indicator, timeout=1)
                            if element:
                                print(f"{Fore.YELLOW}发现登录元素: {indicator}")
                                is_logged_in = False
                                break
                        except:
                            continue
                    else:
                        # 如果没有找到登录元素，假设已登录
                        is_logged_in = True
                        print(f"{Fore.GREEN}未发现登录元素，假设已登录")
                except:
                    is_logged_in = True

            return self.get_account_info_from_current_page(is_logged_in)

        except Exception as e:
            print(f"{Fore.RED}❌ 检查登录状态失败: {e}")
            import traceback
            print(f"{Fore.RED}详细错误: {traceback.format_exc()}")
            return None

    def get_account_info_from_current_page(self, is_logged_in=True):
        """从当前页面获取账户信息"""
        try:
            print(f"{Fore.CYAN}获取账户信息...")

            # 获取 cookies
            cookies = self.page.cookies()
            session_token = None
            auth_token = None

            # 查找 AugmentCode 相关的 cookies，特别是 _session
            print(f"{Fore.CYAN}分析cookies...")
            for cookie in cookies:
                cookie_name = cookie.get("name", "")
                cookie_value = cookie.get("value", "")

                # 特别关注 _session cookie
                if cookie_name == "_session":
                    session_token = cookie_value
                    print(f"{Fore.GREEN}✅ 找到 _session Cookie: {cookie_value[:50]}...")
                elif any(keyword in cookie_name.lower() for keyword in ['session', 'auth', 'token']):
                    print(f"{Fore.GREEN}✅ 找到认证相关 Cookie: {cookie_name}")
                    print(f"{Fore.CYAN}Cookie 值: {cookie_value[:50]}...")

                    if 'auth' in cookie_name.lower() or 'token' in cookie_name.lower():
                        auth_token = cookie_value

            # 尝试从页面获取邮箱
            email = None
            try:
                # 扩展邮箱选择器
                email_selectors = [
                    "xpath://div[contains(text(), '@')]",
                    "xpath://span[contains(text(), '@')]",
                    "xpath://p[contains(text(), '@')]",
                    "xpath://input[@type='email']",
                    "xpath://*[contains(@class, 'email')]",
                    "xpath://*[contains(text(), '@') and string-length(text()) > 5]"
                ]

                for selector in email_selectors:
                    try:
                        email_element = self.page.ele(selector, timeout=1)
                        if email_element:
                            text = email_element.text or email_element.attr('value') or ''
                            if '@' in text and len(text) > 5:
                                email = text.strip()
                                print(f"{Fore.GREEN}从页面获取到邮箱: {email}")
                                break
                    except:
                        continue

                # 如果还没找到，尝试从页面源码中提取
                if not email:
                    try:
                        page_source = self.page.html
                        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                        emails = re.findall(email_pattern, page_source)
                        if emails:
                            # 过滤掉一些常见的非用户邮箱
                            filtered_emails = [e for e in emails if not any(domain in e.lower()
                                             for domain in ['example.com', 'test.com', 'augmentcode.com', 'noreply'])]
                            if filtered_emails:
                                email = filtered_emails[0]
                                print(f"{Fore.GREEN}从页面源码提取到邮箱: {email}")
                    except Exception as e:
                        print(f"{Fore.YELLOW}从页面源码提取邮箱失败: {e}")

            except Exception as e:
                print(f"{Fore.YELLOW}无法从页面获取邮箱: {e}")

            account_info = {
                'session_token': session_token,
                'auth_token': auth_token,
                'token': session_token or auth_token,  # 主要 token
                'email': email,
                'cookies': cookies,
                'current_url': self.page.url,
                'is_logged_in': is_logged_in,
                'timestamp': time.time()
            }

            # 打印关键信息
            if session_token:
                print(f"{Fore.GREEN}✅ 成功获取 _session token")
            else:
                print(f"{Fore.YELLOW}⚠️ 未找到 _session token")

            if is_logged_in:
                print(f"{Fore.GREEN}✅ 用户已登录")
            else:
                print(f"{Fore.YELLOW}⚠️ 用户可能未登录")

            return account_info

        except Exception as e:
            print(f"{Fore.RED}❌ 获取账户信息失败: {e}")
            import traceback
            print(f"{Fore.RED}详细错误: {traceback.format_exc()}")
            return None

    def invite_to_team(self, email_to_invite, session_cookies=None):
        """邀请用户到团队"""
        try:
            print(f"{Fore.CYAN}开始邀请用户到团队: {email_to_invite}")

            # 如果提供了session cookies，先设置它们
            if session_cookies:
                print(f"{Fore.CYAN}设置session cookies...")
                # 先访问app域名
                self.page.get("https://app.augmentcode.com")
                time.sleep(2)

                # 添加cookies
                for cookie in session_cookies:
                    try:
                        # 确保cookie有正确的域名
                        if 'domain' not in cookie or not cookie['domain']:
                            cookie['domain'] = '.augmentcode.com'
                        self.page.set.cookies(cookie)
                    except Exception as e:
                        print(f"{Fore.YELLOW}设置cookie失败: {e}")

            # 访问团队页面
            team_url = "https://app.augmentcode.com/account/team"
            print(f"{Fore.CYAN}访问团队页面: {team_url}")
            self.page.get(team_url)
            time.sleep(3)

            # 检查是否成功访问团队页面
            current_url = self.page.url
            if "login" in current_url.lower():
                print(f"{Fore.RED}❌ 被重定向到登录页面，session可能无效")
                return False

            # 使用requests发送邀请API请求
            import requests

            # 从浏览器获取当前cookies
            browser_cookies = self.page.cookies()

            # 转换为requests格式
            cookies_dict = {}
            for cookie in browser_cookies:
                cookies_dict[cookie['name']] = cookie['value']

            # 检查是否有_session cookie
            if '_session' not in cookies_dict:
                print(f"{Fore.RED}❌ 未找到_session cookie，无法发送邀请")
                return False

            # 准备API请求
            invite_url = "https://app.augmentcode.com/api/team/invite"
            headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Origin': 'https://app.augmentcode.com',
                'Referer': 'https://app.augmentcode.com/account/team',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            }

            payload = {
                "emails": [email_to_invite]
            }

            print(f"{Fore.CYAN}发送团队邀请API请求...")
            print(f"{Fore.CYAN}URL: {invite_url}")
            print(f"{Fore.CYAN}Payload: {payload}")

            # 发送请求
            response = requests.post(
                invite_url,
                json=payload,
                headers=headers,
                cookies=cookies_dict,
                timeout=30
            )

            print(f"{Fore.CYAN}API响应状态码: {response.status_code}")

            if response.status_code == 200:
                print(f"{Fore.GREEN}✅ 团队邀请发送成功！")
                try:
                    response_data = response.json()
                    print(f"{Fore.GREEN}响应数据: {response_data}")
                except:
                    print(f"{Fore.GREEN}响应内容: {response.text}")
                return True
            else:
                print(f"{Fore.RED}❌ 团队邀请失败")
                print(f"{Fore.RED}状态码: {response.status_code}")
                print(f"{Fore.RED}响应内容: {response.text}")
                return False

        except Exception as e:
            print(f"{Fore.RED}❌ 团队邀请过程失败: {e}")
            import traceback
            print(f"{Fore.RED}详细错误: {traceback.format_exc()}")
            return False

    def run_auto_registration(self):
        """运行完全自动注册流程"""
        print(f"{Fore.CYAN}{'='*60}")
        print(f"{Fore.CYAN}AugmentCode 完全自动注册工具")
        print(f"{Fore.CYAN}{'='*60}")

        try:
            # 生成注册信息（只需要邮箱）
            email = self.generate_email()

            print(f"{Fore.YELLOW}📋 注册信息:")
            print(f"  邮箱: {email}")
            print()

            # 1. 设置浏览器
            if not self.setup_driver():
                return False

            # 2. 导航到Auth0注册页面（只访问一次）
            if not self.navigate_to_auth_page():
                return False

            # 等待页面完全加载
            print(f"{Fore.CYAN}等待Auth0页面完全加载...")
            time.sleep(5)

            # 3. 填写邮箱表单
            if self.fill_email_form(email):
                print(f"{Fore.GREEN}✅ 邮箱表单提交成功")

                # 4. 自动处理验证码
                if self.handle_verification_code_auto(email):
                    print(f"{Fore.GREEN}✅ 验证码处理成功")

                    # 5. 检查登录状态并获取session信息
                    account_info = self.check_login_status_and_get_session()
                    if account_info:
                        print(f"{Fore.GREEN}✅ 注册完成！")

                        # 保存结果
                        result = {
                            'success': True,
                            'registration_email': email,
                            'account_email': account_info.get('email', email),
                            'token': account_info.get('token'),
                            'session_token': account_info.get('session_token'),
                            'auth_token': account_info.get('auth_token'),
                            'final_url': account_info.get('current_url', self.page.url),
                            'cookies': account_info.get('cookies', []),
                            'is_logged_in': account_info.get('is_logged_in', False),
                            'timestamp': time.time()
                        }

                        # 保存到文件
                        result_file = f'augment_auto_register_result_{int(time.time())}.json'
                        with open(result_file, 'w', encoding='utf-8') as f:
                            json.dump(result, f, indent=2, ensure_ascii=False, default=str)

                        print(f"{Fore.GREEN}🎉 注册成功总结:")
                        print(f"  📧 邮箱: {result['account_email']}")
                        print(f"  🔑 Token: {result['token'][:20] if result['token'] else 'N/A'}...")
                        print(f"  💾 结果已保存到: {result_file}")

                        return result
                    else:
                        print(f"{Fore.RED}❌ 获取账户信息失败")
                else:
                    print(f"{Fore.RED}❌ 验证码处理失败")
            else:
                print(f"{Fore.RED}❌ 邮箱表单提交失败")

            return False

        except Exception as e:
            print(f"{Fore.RED}❌ 注册过程异常: {e}")
            import traceback
            print(f"{Fore.RED}详细错误: {traceback.format_exc()}")
            return False

        finally:
            if self.page:
                try:
                    input(f"{Fore.YELLOW}按 Enter 键关闭浏览器...")
                    self.page.quit()
                except:
                    pass


def main():
    """主函数"""
    try:
        # 创建注册器
        register = AugmentAutoRegister()

        # 运行自动注册
        result = register.run_auto_registration()

        if result:
            print(f"{Fore.GREEN}\n🎉 自动注册成功！")

            # 检查是否有session token，如果有，询问是否要测试团队邀请功能
            if result.get('session_token') and result.get('is_logged_in'):
                print(f"{Fore.CYAN}\n检测到有效的session token，可以测试团队邀请功能")

                # 询问用户是否要测试团队邀请
                try:
                    test_invite = input(f"{Fore.YELLOW}是否要测试团队邀请功能？(y/n): ").strip().lower()
                    if test_invite == 'y':
                        invite_email = input(f"{Fore.YELLOW}请输入要邀请的邮箱地址: ").strip()
                        if invite_email and '@' in invite_email:
                            print(f"{Fore.CYAN}测试邀请邮箱: {invite_email}")
                            invite_success = register.invite_to_team(invite_email, result.get('cookies'))
                            if invite_success:
                                print(f"{Fore.GREEN}✅ 团队邀请测试成功！")
                            else:
                                print(f"{Fore.RED}❌ 团队邀请测试失败")
                        else:
                            print(f"{Fore.YELLOW}邮箱地址无效，跳过团队邀请测试")
                except:
                    print(f"{Fore.YELLOW}跳过团队邀请测试")
            else:
                print(f"{Fore.YELLOW}\n⚠️ 未获取到有效的session token，无法测试团队邀请功能")
        else:
            print(f"{Fore.RED}\n❌ 自动注册失败")

    except KeyboardInterrupt:
        print(f"{Fore.YELLOW}\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"{Fore.RED}\n❌ 程序异常: {e}")


if __name__ == "__main__":
    main()
