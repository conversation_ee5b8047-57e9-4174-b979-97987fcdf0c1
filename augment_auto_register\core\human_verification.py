#!/usr/bin/env python3
"""
Augment 自动注册工具 - 人机验证模块
处理自动人机验证，检测验证状态（success/verifying/failed/expired）
"""

import time
from typing import Optional, Literal
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.config_manager import get_config
from utils.logger import get_logger
from utils.helpers import safe_sleep


class VerificationStatus(Enum):
    """验证状态枚举"""
    SUCCESS = "success"
    VERIFYING = "verifying"
    FAILED = "failed"
    EXPIRED = "expired"
    PENDING = "pending"
    UNKNOWN = "unknown"


class HumanVerificationHandler:
    """人机验证处理器"""
    
    def __init__(self, browser_manager, config_manager=None):
        """
        初始化人机验证处理器
        
        Args:
            browser_manager: 浏览器管理器实例
            config_manager: 配置管理器实例
        """
        self.browser = browser_manager
        self.config = config_manager or get_config()
        self.logger = get_logger("HumanVerification")
        
        # 获取验证配置
        self.verification_wait = self.config.get('registration.verification_wait', 15)
        self.max_retries = self.config.get('registration.max_retries', 3)
        
        self.logger.info("人机验证处理器初始化完成")
    
    def handle_verification(self) -> bool:
        """
        处理人机验证主流程

        Returns:
            是否验证成功
        """
        self.logger.info("开始处理人机验证...")

        # 先快速检查一次是否已经成功
        initial_status = self.check_verification_status()
        if initial_status == VerificationStatus.SUCCESS:
            self.logger.success("✅ 人机验证已经完成！")
            return True

        # 短暂等待让页面状态稳定
        self.logger.info("等待页面状态稳定...")
        safe_sleep(1)

        # 再次检查，如果需要则点击复选框
        status_after_wait = self.check_verification_status()
        self.logger.info(f"📊 等待后的验证状态: {status_after_wait.value}")

        if status_after_wait == VerificationStatus.SUCCESS:
            self.logger.success("✅ 人机验证已经完成！")
            return True
        elif status_after_wait == VerificationStatus.PENDING:
            self.logger.info("🔲 检测到需要点击复选框，开始点击...")
            # 尝试点击复选框触发验证
            if self._click_verification_checkbox():
                self.logger.info("已点击验证复选框，等待5秒让验证完成...")
                safe_sleep(5)  # 等待验证完成

                # 点击后再次检查状态
                status_after_click = self.check_verification_status()
                if status_after_click == VerificationStatus.SUCCESS:
                    self.logger.success("✅ 点击后人机验证已完成！")
                    return True


        # 等待自动验证完成
        return self.wait_for_auto_verification()

    def _click_verification_checkbox(self) -> bool:
        """
        点击人机验证复选框

        Returns:
            是否成功点击
        """
        self.logger.info("🔍 开始查找人机验证复选框...")

        checkbox_selectors = [
            "input[type='checkbox']",
            "@type=checkbox",
            "xpath://input[@type='checkbox']"
            
        ]

        for i, selector in enumerate(checkbox_selectors):
            self.logger.info(f"🔍 尝试选择器 {i+1}: {selector}")
            element = self.browser.find_element(selector, timeout=1)
            if element:
                self.logger.success(f"✅ 找到复选框元素: {selector}")
                try:
                    self.logger.info(f"🔘 尝试点击复选框...")
                    element.click()
                    self.logger.success(f"✅ 成功点击验证复选框: {selector}")
                    return True
                except Exception as e:
                    self.logger.warning(f"❌ 点击复选框失败: {e}")
                    continue
            else:
                self.logger.info(f"❌ 未找到元素: {selector}")

        self.logger.error("❌ 所有选择器都未找到可点击的验证复选框")
        return False
    
    def wait_for_auto_verification(self, max_wait: Optional[int] = None) -> bool:
        """
        等待自动验证完成
        
        Args:
            max_wait: 最大等待时间（秒），None使用配置值
            
        Returns:
            是否验证成功
        """
        if max_wait is None:
            max_wait = self.verification_wait
        
        self.logger.info(f"等待自动人机验证完成（最多等待{max_wait}秒）...")
        
        check_interval = 1  # 每1秒检查一次
        
        for i in range(max_wait):
            elapsed = i + 1
            self.logger.debug(f"检查验证状态... ({elapsed}/{max_wait})")
            
            status = self.check_verification_status()
            
            if status == VerificationStatus.SUCCESS:
                self.logger.success("✅ 人机验证自动完成！")
                return True
            elif status == VerificationStatus.FAILED:
                self.logger.error("❌ 人机验证失败")
                return False
            elif status == VerificationStatus.EXPIRED:
                self.logger.warning("⏰ 人机验证过期")
                return False
            elif status == VerificationStatus.VERIFYING:
                self.logger.info("🔄 正在验证中，继续等待...")
            elif status == VerificationStatus.PENDING:
                self.logger.debug("⏳ 等待验证开始...")
            else:
                self.logger.debug("❓ 验证状态未知，继续等待...")
            
            safe_sleep(check_interval)
        
        self.logger.error(f"❌ 人机验证超时（{max_wait}秒）")
        return False
    
    def check_verification_status(self) -> VerificationStatus:
        """
        检查人机验证状态

        Returns:
            当前验证状态
        """
        try:
            # 核心方法：检查是否存在复选框
            # 如果有复选框，说明需要验证；如果没有，说明已经验证成功
            checkbox_selectors = [
                "input[type='checkbox']",
                "@type=checkbox",
                "xpath://input[@type='checkbox']"
            ]

            checkbox_found = False
            for selector in checkbox_selectors:
                element = self.browser.find_element(selector, timeout=0.5)
                if element:
                    checkbox_found = True
                    self.logger.debug(f"找到复选框: {selector}")

                    # 检查复选框是否已勾选
                    is_checked = element.attr('checked')
                    if is_checked:
                        self.logger.debug("复选框已勾选，验证可能正在进行")
                        return VerificationStatus.VERIFYING
                    else:
                        self.logger.debug("复选框未勾选，需要点击验证")
                        return VerificationStatus.PENDING
                    break

            # 如果没有找到复选框，说明验证已经成功
            if not checkbox_found:
                self.logger.debug("未找到复选框，人机验证已成功")
                return VerificationStatus.SUCCESS

            # 备用方法1: 检查"Success!"文本（双重确认）
            success_text_selectors = [
                "text:Success!",
                "text:success",
                "text:Success",
                "xpath://div[contains(text(), 'Success')]",
                "xpath://*[contains(text(), 'Success')]"
            ]

            for selector in success_text_selectors:
                element = self.browser.find_element(selector, timeout=0.5)
                if element and self.browser.is_element_visible(selector):
                    self.logger.debug(f"找到成功文本: {selector}")
                    return VerificationStatus.SUCCESS

            # 备用方法2: 检查Continue按钮是否可用
            continue_selectors = [
                "@type=submit",
                "button[type='submit']",
                "text:Continue",
                "xpath://button[contains(text(), 'Continue')]"
            ]

            for selector in continue_selectors:
                element = self.browser.find_element(selector, timeout=0.5)
                if element:
                    # 检查按钮是否启用
                    disabled = element.attr('disabled')
                    if not disabled or disabled == 'false':
                        self.logger.debug(f"Continue按钮已启用，验证可能成功")
                        return VerificationStatus.SUCCESS

            # 默认状态
            return VerificationStatus.PENDING

        except Exception as e:
            self.logger.error(f"检查验证状态失败: {e}")
            return VerificationStatus.UNKNOWN
    
    def get_verification_message(self) -> Optional[str]:
        """
        获取验证相关的消息文本
        
        Returns:
            验证消息，如果没有则返回None
        """
        try:
            # 尝试获取各种状态的消息
            message_selectors = [
                "@id=success-text",      # 成功消息
                "@id=verifying-text",    # 验证中消息
                "@id=fail-text",         # 失败消息
                "@id=expired-text",      # 过期消息
                "@id=timeout-text"       # 超时消息
            ]
            
            for selector in message_selectors:
                element = self.browser.find_element(selector, timeout=1)
                if element and self.browser.is_element_visible(selector):
                    message = self.browser.get_element_text(selector)
                    if message:
                        return message.strip()
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取验证消息失败: {e}")
            return None
    
    def handle_verification_failure(self) -> bool:
        """
        处理验证失败的情况
        
        Returns:
            是否需要重试
        """
        self.logger.warning("处理验证失败情况...")
        
        # 获取失败消息
        message = self.get_verification_message()
        if message:
            self.logger.warning(f"验证失败消息: {message}")
        
        # 检查是否可以重试
        status = self.check_verification_status()
        
        if status == VerificationStatus.EXPIRED:
            self.logger.info("验证已过期，可能需要刷新页面")
            return False
        elif status == VerificationStatus.FAILED:
            self.logger.info("验证失败，可能需要重新尝试")
            return False
        
        return False
    
    def get_status_summary(self) -> dict:
        """
        获取验证状态摘要
        
        Returns:
            状态摘要字典
        """
        status = self.check_verification_status()
        message = self.get_verification_message()
        
        return {
            'status': status.value,
            'message': message,
            'timestamp': time.time(),
            'is_success': status == VerificationStatus.SUCCESS,
            'is_failed': status in [VerificationStatus.FAILED, VerificationStatus.EXPIRED]
        }
    
    def __str__(self) -> str:
        """返回人机验证处理器的字符串表示"""
        return f"HumanVerificationHandler(max_wait={self.verification_wait}s)"
    
    def __repr__(self) -> str:
        return self.__str__()


if __name__ == "__main__":
    # 测试人机验证处理器
    from ..utils.logger import setup_logger
    from .browser_manager import BrowserManager
    
    # 设置日志
    setup_logger(level="DEBUG")
    
    print("🧪 测试人机验证处理器")
    
    # 创建浏览器管理器（模拟）
    class MockBrowserManager:
        def find_element(self, selector, timeout=10):
            return None
        
        def is_element_visible(self, selector):
            return False
        
        def get_element_text(self, selector):
            return None
    
    # 创建人机验证处理器
    browser = MockBrowserManager()
    verifier = HumanVerificationHandler(browser)
    
    # 测试状态检查
    status = verifier.check_verification_status()
    print(f"验证状态: {status.value}")
    
    # 测试状态摘要
    summary = verifier.get_status_summary()
    print(f"状态摘要: {summary}")
    
    print("✅ 人机验证处理器测试完成")
