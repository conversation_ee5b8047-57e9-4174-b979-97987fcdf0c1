#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Vim插件OAuth URL生成器
按照Vim插件的格式构建OAuth授权URL来获取code
"""

import uuid
import base64
import hashlib
import secrets
from urllib.parse import urlencode

class VimAugmentOAuth:
    """
    模拟Vim插件的OAuth实现
    基于对augment.vim插件代码的分析
    """
    
    def __init__(self):
        # 基于Vim插件分析得出的OAuth参数
        self.client_id = "v"  # Vim插件使用的client_id
        self.auth_base_url = "https://auth.augmentcode.com/authorize"
        self.scope = "email"
        
        # Vim插件可能不使用redirect_uri，或使用特殊的URI
        # 从代码分析看，Vim插件是手动输入code的方式
        self.redirect_uri = None  # Vim插件可能不需要redirect_uri
        
        # PKCE参数存储
        self.code_verifier = None
        self.code_challenge = None
        self.state = None
    
    def generate_pkce(self):
        """
        生成PKCE参数
        按照OAuth 2.1 PKCE标准实现
        """
        # 生成32字节随机code_verifier
        self.code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
        
        # 生成code_challenge (SHA256哈希)
        self.code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(self.code_verifier.encode()).digest()
        ).decode('utf-8').rstrip('=')
        
        return self.code_verifier, self.code_challenge
    
    def generate_vim_auth_url(self):
        """
        生成Vim插件格式的OAuth授权URL
        模拟dist/server.js中的URL生成逻辑
        """
        # 生成随机state参数
        self.state = str(uuid.uuid4())
        
        # 生成PKCE参数
        self.generate_pkce()
        
        # 构建OAuth参数 - 按照Vim插件的格式
        params = {
            'response_type': 'code',
            'client_id': self.client_id,
            'scope': self.scope,
            'state': self.state,
            'code_challenge': self.code_challenge,
            'code_challenge_method': 'S256'
        }
        
        # Vim插件可能不使用redirect_uri
        # 因为它是通过手动输入code的方式
        
        # 构建完整的授权URL
        auth_url = f"{self.auth_base_url}?{urlencode(params)}"
        
        return auth_url
    
    def print_vim_oauth_info(self):
        """
        打印Vim插件格式的OAuth信息
        """
        print("🔧 Vim Augment插件OAuth参数:")
        print("=" * 50)
        print(f"🏢 Client ID: {self.client_id}")
        print(f"🌐 Auth URL: {self.auth_base_url}")
        print(f"📧 Scope: {self.scope}")
        print(f"🔑 State: {self.state}")
        print(f"🔐 Code Verifier: {self.code_verifier}")
        print(f"🔐 Code Challenge: {self.code_challenge}")
        print(f"🔒 Challenge Method: S256")
        print()
    
    def get_vim_oauth_flow_instructions(self):
        """
        获取Vim插件OAuth流程说明
        """
        return """
📋 Vim插件OAuth流程说明:
1. Vim插件发送 'augment/login' 请求到本地server.js
2. server.js生成OAuth URL并返回给Vim插件
3. Vim插件显示URL，用户手动访问浏览器
4. 用户登录并授权，获得授权码
5. 用户手动输入授权码到Vim插件
6. Vim插件发送 'augment/token' 请求交换token

🎯 我们的目标:
模拟步骤2，生成与Vim插件相同格式的OAuth URL
"""

def main():
    print("🚀 Vim Augment插件OAuth URL生成器")
    print("=" * 50)
    
    # 创建OAuth实例
    vim_oauth = VimAugmentOAuth()
    
    # 显示流程说明
    print(vim_oauth.get_vim_oauth_flow_instructions())
    
    # 生成OAuth URL
    auth_url = vim_oauth.generate_vim_auth_url()
    
    # 显示OAuth参数信息
    vim_oauth.print_vim_oauth_info()
    
    # 显示生成的URL
    print("🔗 生成的OAuth授权URL:")
    print("-" * 50)
    print(auth_url)
    print()
    
    # 显示使用说明
    print("📋 使用说明:")
    print("1. 复制上面的OAuth URL到浏览器")
    print("2. 登录你的Augment账号并授权")
    print("3. 授权后会跳转到一个页面")
    print("4. 从页面或URL中获取以下信息:")
    print("   - authorization code (授权码)")
    print("   - tenant_url (租户URL)")
    print()
    
    # 保存PKCE参数供后续使用
    print("🔐 重要: 保存以下PKCE参数用于token交换:")
    print(f"Code Verifier: {vim_oauth.code_verifier}")
    print(f"State: {vim_oauth.state}")
    print()
    
    print("⚠️  注意: 这些参数在token交换时必须使用!")

if __name__ == "__main__":
    main()
