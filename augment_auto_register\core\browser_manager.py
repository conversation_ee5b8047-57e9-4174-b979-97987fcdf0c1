#!/usr/bin/env python3
"""
Augment 自动注册工具 - 浏览器管理模块
负责浏览器启动、页面导航、元素定位和操作
"""

import os
import sys
import time
from typing import Optional, List, Any
from pathlib import Path

from DrissionPage import ChromiumOptions, ChromiumPage
from DrissionPage.errors import ElementNotFoundError, PageDisconnectedError

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.config_manager import get_config
from utils.logger import get_logger
from utils.helpers import get_random_wait_time, safe_sleep


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, config_manager=None):
        """
        初始化浏览器管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager or get_config()
        self.logger = get_logger("BrowserManager")
        
        self.page: Optional[ChromiumPage] = None
        self.is_initialized = False
        
        # 获取浏览器配置
        self.browser_config = self.config.get_browser_config()
        self.headless = self.browser_config.get('headless', False)
        self.timeout = self.browser_config.get('timeout', 30)
        self.chrome_path = self.browser_config.get('chrome_path', '')
        
        self.logger.info("浏览器管理器初始化完成")
    
    def setup_browser(self) -> bool:
        """
        设置并启动浏览器
        
        Returns:
            是否成功启动
        """
        try:
            self.logger.info("正在启动浏览器...")
            
            # 获取Chrome路径
            chrome_path = self._get_chrome_path()
            
            # 设置浏览器选项
            co = ChromiumOptions()
            co.set_browser_path(chrome_path)
            
            # 只在Linux上添加--no-sandbox
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")
            
            # 设置无头模式
            co.headless(self.headless)
            
            # 自动分配端口
            co.auto_port()
            
            # 启动浏览器
            self.page = ChromiumPage(co)
            safe_sleep(1)
            
            self.is_initialized = True
            self.logger.success(f"浏览器启动成功 (Chrome: {chrome_path})")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器启动失败: {e}")
            return False
    
    def _get_chrome_path(self) -> str:
        """获取Chrome浏览器路径"""
        if self.chrome_path and os.path.exists(self.chrome_path):
            return self.chrome_path
        
        # 自动查找Chrome路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(
                os.getenv('USERNAME', '')
            )
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                self.logger.info(f"找到Chrome浏览器: {path}")
                return path
        
        # 使用默认路径
        self.logger.warning("未找到Chrome浏览器，使用默认路径")
        return chrome_paths[0]
    
    def navigate_to_page(self, url: str) -> bool:
        """
        导航到指定页面
        
        Args:
            url: 目标URL
            
        Returns:
            是否成功导航
        """
        if not self.is_initialized:
            self.logger.error("浏览器未初始化")
            return False
        
        try:
            self.logger.info(f"导航到页面: {url}")
            
            # 先访问空白页
            self.page.get('about:blank')
            safe_sleep(get_random_wait_time('page_load'))
            
            # 访问目标页面
            self.page.get(url)
            safe_sleep(get_random_wait_time('page_load'))
            
            current_url = self.page.url
            self.logger.info(f"当前页面URL: {current_url}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"页面导航失败: {e}")
            return False
    
    def wait_for_page_load(self, max_wait: int = 15) -> bool:
        """
        等待页面加载完成
        
        Args:
            max_wait: 最大等待时间（秒）
            
        Returns:
            是否加载完成
        """
        try:
            # 等待页面加载
            for i in range(max_wait):
                if self.page.ready_state == 'complete':
                    self.logger.debug("页面加载完成")
                    return True
                safe_sleep(1)
            
            self.logger.warning(f"页面加载超时 ({max_wait}秒)")
            return False
            
        except Exception as e:
            self.logger.error(f"等待页面加载失败: {e}")
            return False

    def find_element(self, selector: str, timeout: int = 10) -> Optional[Any]:
        """
        查找页面元素

        Args:
            selector: 元素选择器
            timeout: 超时时间

        Returns:
            找到的元素，未找到返回None
        """
        try:
            element = self.page.ele(selector, timeout=timeout)
            if element:
                self.logger.debug(f"找到元素: {selector}")
                return element
            else:
                self.logger.warning(f"未找到元素: {selector}")
                return None

        except ElementNotFoundError:
            self.logger.warning(f"元素不存在: {selector}")
            return None
        except Exception as e:
            self.logger.error(f"查找元素失败: {e}")
            return None

    def find_elements(self, selector: str, timeout: int = 10) -> List[Any]:
        """
        查找多个页面元素

        Args:
            selector: 元素选择器
            timeout: 超时时间

        Returns:
            找到的元素列表
        """
        try:
            elements = self.page.eles(selector, timeout=timeout)
            self.logger.debug(f"找到 {len(elements)} 个元素: {selector}")
            return elements

        except Exception as e:
            self.logger.error(f"查找多个元素失败: {e}")
            return []

    def fill_input(self, selector: str, text: str, clear_first: bool = True) -> bool:
        """
        填写输入框

        Args:
            selector: 输入框选择器
            text: 要输入的文本
            clear_first: 是否先清空

        Returns:
            是否成功填写
        """
        try:
            element = self.find_element(selector)
            if not element:
                return False

            if clear_first:
                element.clear()
                safe_sleep(0.3)

            element.input(text)
            safe_sleep(get_random_wait_time('input'))

            self.logger.info(f"成功填写输入框: {selector}")
            return True

        except Exception as e:
            self.logger.error(f"填写输入框失败: {e}")
            return False

    def click_element(self, selector: str) -> bool:
        """
        点击元素

        Args:
            selector: 元素选择器

        Returns:
            是否成功点击
        """
        try:
            element = self.find_element(selector)
            if not element:
                return False

            element.click()
            safe_sleep(get_random_wait_time('click'))

            self.logger.info(f"成功点击元素: {selector}")
            return True

        except Exception as e:
            self.logger.error(f"点击元素失败: {e}")
            return False

    def get_element_text(self, selector: str) -> Optional[str]:
        """
        获取元素文本

        Args:
            selector: 元素选择器

        Returns:
            元素文本，未找到返回None
        """
        try:
            element = self.find_element(selector)
            if element:
                text = element.text
                self.logger.debug(f"获取元素文本: {selector} -> {text}")
                return text
            return None

        except Exception as e:
            self.logger.error(f"获取元素文本失败: {e}")
            return None

    def is_element_visible(self, selector: str) -> bool:
        """
        检查元素是否可见

        Args:
            selector: 元素选择器

        Returns:
            是否可见
        """
        try:
            element = self.find_element(selector, timeout=2)
            if element:
                # 检查元素的display和visibility样式
                display = element.style('display')
                visibility = element.style('visibility')

                is_visible = (display != 'none' and visibility != 'hidden')
                self.logger.debug(f"元素可见性: {selector} -> {is_visible}")
                return is_visible

            return False

        except Exception as e:
            self.logger.error(f"检查元素可见性失败: {e}")
            return False

    def wait_for_element(self, selector: str, timeout: int = 10) -> Optional[Any]:
        """
        等待元素出现

        Args:
            selector: 元素选择器
            timeout: 超时时间

        Returns:
            找到的元素，超时返回None
        """
        try:
            for i in range(timeout):
                element = self.find_element(selector, timeout=1)
                if element:
                    self.logger.debug(f"元素已出现: {selector}")
                    return element
                safe_sleep(1)

            self.logger.warning(f"等待元素超时: {selector}")
            return None

        except Exception as e:
            self.logger.error(f"等待元素失败: {e}")
            return None

    def get_current_url(self) -> str:
        """
        获取当前页面URL

        Returns:
            当前URL
        """
        try:
            if self.page:
                return self.page.url
            return ""
        except Exception as e:
            self.logger.error(f"获取当前URL失败: {e}")
            return ""

    def get_page_title(self) -> str:
        """
        获取页面标题

        Returns:
            页面标题
        """
        try:
            if self.page:
                return self.page.title
            return ""
        except Exception as e:
            self.logger.error(f"获取页面标题失败: {e}")
            return ""

    def close_browser(self) -> None:
        """关闭浏览器"""
        try:
            if self.page:
                self.page.quit()
                self.page = None
                self.is_initialized = False
                self.logger.info("浏览器已关闭")
        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        self.setup_browser()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_browser()

    def __str__(self) -> str:
        """返回浏览器管理器的字符串表示"""
        status = "已初始化" if self.is_initialized else "未初始化"
        return f"BrowserManager(status={status}, headless={self.headless})"

    def __repr__(self) -> str:
        return self.__str__()
