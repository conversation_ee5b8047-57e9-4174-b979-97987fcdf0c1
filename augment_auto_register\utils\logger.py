#!/usr/bin/env python3
"""
Augment 自动注册工具 - 日志管理模块
使用loguru提供彩色日志输出和文件保存功能
"""

import sys
from pathlib import Path
from typing import Optional
from loguru import logger


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, 
                 level: str = "INFO",
                 save_to_file: bool = True,
                 log_file: Optional[str] = None):
        """
        初始化日志管理器
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            save_to_file: 是否保存日志到文件
            log_file: 日志文件路径
        """
        self.level = level.upper()
        self.save_to_file = save_to_file
        
        if log_file is None:
            # 默认日志文件路径
            project_root = Path(__file__).parent.parent
            logs_dir = project_root / "logs"
            logs_dir.mkdir(exist_ok=True)
            log_file = logs_dir / "augment_register.log"
        
        self.log_file = Path(log_file)
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """设置日志器"""
        # 移除默认的日志处理器
        logger.remove()
        
        # 添加控制台输出（彩色）
        logger.add(
            sys.stdout,
            level=self.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 添加文件输出（如果启用）
        if self.save_to_file:
            # 确保日志目录存在
            self.log_file.parent.mkdir(parents=True, exist_ok=True)
            
            logger.add(
                str(self.log_file),
                level=self.level,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
                rotation="10 MB",  # 日志文件大小超过10MB时轮转
                retention="7 days",  # 保留7天的日志文件
                compression="zip",  # 压缩旧的日志文件
                encoding="utf-8"
            )
    
    def get_logger(self, name: Optional[str] = None):
        """
        获取日志器实例
        
        Args:
            name: 日志器名称
            
        Returns:
            loguru.logger实例
        """
        if name:
            return logger.bind(name=name)
        return logger
    
    def set_level(self, level: str) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        self.level = level.upper()
        self._setup_logger()


# 全局日志管理器实例
_logger_manager: Optional[LoggerManager] = None


def setup_logger(level: str = "INFO",
                 save_to_file: bool = True,
                 log_file: Optional[str] = None) -> LoggerManager:
    """
    设置全局日志管理器
    
    Args:
        level: 日志级别
        save_to_file: 是否保存到文件
        log_file: 日志文件路径
        
    Returns:
        LoggerManager实例
    """
    global _logger_manager
    _logger_manager = LoggerManager(level, save_to_file, log_file)
    return _logger_manager


def get_logger(name: Optional[str] = None):
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        loguru.logger实例
    """
    global _logger_manager
    
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    
    return _logger_manager.get_logger(name)


# 便捷的日志函数
def log_info(message: str, name: Optional[str] = None) -> None:
    """记录信息日志"""
    get_logger(name).info(message)


def log_warning(message: str, name: Optional[str] = None) -> None:
    """记录警告日志"""
    get_logger(name).warning(message)


def log_error(message: str, name: Optional[str] = None) -> None:
    """记录错误日志"""
    get_logger(name).error(message)


def log_debug(message: str, name: Optional[str] = None) -> None:
    """记录调试日志"""
    get_logger(name).debug(message)


def log_success(message: str, name: Optional[str] = None) -> None:
    """记录成功日志"""
    get_logger(name).success(message)


if __name__ == "__main__":
    # 测试日志管理器
    setup_logger(level="DEBUG")
    
    logger = get_logger("test")
    
    logger.debug("这是调试信息")
    logger.info("这是普通信息")
    logger.success("这是成功信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    
    # 测试便捷函数
    log_info("使用便捷函数记录信息", "convenience")
    log_success("日志管理器测试完成", "test")
